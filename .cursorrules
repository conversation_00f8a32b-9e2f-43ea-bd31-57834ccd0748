## 技术栈规范
- 使用 TypeScript + React + Antd 5.x 进行开发
- 使用 Vite 作为构建工具
- 若需进行状态管理，优先使用 zustand
- 若需使用自定义hooks，优先使用 ahooks 提供的 hooks 工具库
- 使用 Quill 作为富文本编辑器

## 代码风格和结构
- 编写简洁、可维护的 TypeScript 代码
- 使用函数式组件，避免使用类组件
- 优先使用组合而非继承
- 使用描述性的变量命名(例如: isLoading, hasError)
- 按照功能模块化组织代码结构

## TypeScript 使用
- 优先使用 interface 定义类型
- 避免使用 any，必要时使用 unknown
- 为组件props定义清晰的类型接口
- 使用 enum 定义常量枚举值
- 使用类型断言时需要添加注释说明

## TypeScript类型定义规范

- API相关类型定义
    - 若需使用API类型定义，优先从 APISpec 从 @mfe/cc-api-caller-pc 获取类型定义
    - 请求类型命名规则：`${ModuleName}Request`，例如：`KnowledgeDetailRequest`
    - 响应类型命名规则：`${ModuleName}Response`，例如：`KnowledgeDetailResponse`
    - 列表项类型命名规则：`${ModuleName}Item`，例如：`KnowledgeDetailItem`

- 类型定义文件结构
    ```ts
    import { APISpec } from '@mfe/cc-api-caller-pc';

    // API请求类型
    export type ExampleRequest = APISpec['/path/to/api']['request'];

    // API响应类型
    export type ExampleResponse = APISpec['/path/to/api']['response'];

    // 列表项类型
    export type ExampleItem = ExampleResponse['dataList'][0];

    // 业务类型
    export type BusinessType = {
        // 自定义业务类型
    };
    ```

- 类型分组顺序
    - API请求类型（Request）
    - API响应类型（Response）
    - 业务类型（自定义类型）

- 类型定义原则
    - 避免重复定义已存在于 APISpec 中的类型
    - 对于复杂的嵌套类型，使用类型别名简化
    - 为所有类型添加 export 关键字
    - 优先使用 type 而不是 interface
    - 必要时使用 Pick、Omit 等工具类型

- 类型注释
    - 对于复杂的类型定义添加注释说明
    - 对于特殊的类型约束添加注释说明
    - 对于业务相关的枚举值添加注释说明

- 类型检查
    - 确保所有 API 调用都有正确的类型定义
    - 避免使用 any 类型
    - 必要时使用类型断言，并添加注释说明原因

- 最佳实践
    - 保持类型定义的简单性和可读性
    - 遵循 DRY 原则，避免重复的类型定义
    - 及时更新类型定义以匹配 API 变化
    - 使用 TypeScript 的严格模式

## 组件开发
- 组件职责单一，避免过度耦合
- 抽取可复用的逻辑到自定义 hooks
- 使用 ConfigProvider 统一配置组件主题
- 合理使用 Context 共享状态

## 文件命名规范
- 页面入口文件，使用 main.tsx 命名，通常情况下只需要变更render 函数的第二个参数（页面标题）, 代码示例为
    ```ts
    import { render } from '@src/module/root';
    import App from './App';
    import Config from '@src/pages/knowledge/components/config';
    import './style.scss';

    render(
        <Config>
            <App />
        </Config>,
        '知识上传'
    )
    ```

- 页面索引文件，使用 App.tsx 命名
- 样式文件，默认使用 style.scss 命名
- 组件文件使用 PascalCase 命名规则

## 网络请求规范
- 使用 apiCaller 进行网络请求
- 从 @mfe/cc-api-caller-pc 引入 apiCaller 和 APISpec
- 利用 apiCaller 的 send 方法进行网络请求
- 默认res.code !== 0 为错误可直接return，使用模板参考    
   ```ts
    const fetchEvaluationInfo = async (params: SearchParams) => {
        const res = await apiCaller.send('/impc/service/getServicePoints', params);

        if (res.code !== 0) {
            return;
        }

        return res.data;
    };
   ```

## 样式规范
- 使用 SCSS 编写样式
- 遵循 BEM 命名规范
- 避免内联样式，除非动态计算
- 响应式设计使用相对单位

## 状态管理
- 使用 zustand 管理全局状态
- 组件内部状态使用 useState
- 复杂表单状态使用 Form.useForm
- 使用 useWatch 监听表单值变化

## 错误处理
- 统一的错误处理机制
- API 调用使用 try-catch
- 友好的错误提示
- 使用 owl 进行错误监控

## 性能优化
- 合理使用 useMemo 和 useCallback
- 避免不必要的重渲染
- 图片资源按需加载
- 路由组件懒加载
