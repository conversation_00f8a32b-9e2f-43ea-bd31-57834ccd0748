# 知识库首页

## 页面概述

知识库首页作为知识管理的主入口，展示知识库概览信息及数据统计。页面采用卡片式布局，清晰展示各知识库的基本信息。

## 原型图

![image](./image.png)

## 接口文档

接口定义详见 `apiSpec/autoGeneratedKnowledge.d.ts`

## 功能模块

### 1. 顶部数据统计

展示三个核心数据指标:

-   知识库总数: 后台所有知识切片求和（每日更新）
-   本周活跃知识: 本周被使用的知识切片数量(每周更新)
-   本周更新知识: 本周新增的知识切片数量(每周更新)

### 2. 知识库列表

采用网格布局展示所有知识库卡片，每个卡片包含:

#### 卡片内容

-   知识库名称: 展示知识库标题
-   简介/关键词: 不超过 20 字的知识库简述
-   更新时间: 格式为 yyyy-mm-dd h:mm:ss
-   切片数量: 显示库内知识切片总数(包含段落、图表、FAQ 等)

#### 交互说明

-   点击卡片: 跳转至对应知识库详情页面
-   卡片布局: 采用网格式布局，每行显示 3 个卡片

### 3. 默认知识库

系统初始化包含 6 个默认知识库:

-   直营自领知识库
-   直营校园知识库
-   拼好饭知识库
-   全国 KA 知识库
-   区域 KA 知识库
-   远程运营知识库

### 4. 页面操作

-   右上角设置"上传知识"入口按钮
-   点击后跳转至知识上传页面，window.open('/knowledge/upload')

## 技术实现要点

### 组件结构

在 src/pages/knowledge/home/<USER>

- knowledgeCard.tsx 知识库卡片组件
