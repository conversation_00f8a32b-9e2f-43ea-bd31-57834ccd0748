{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "types": ["vite/client", "vite-plugin-svgr/client"], "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "noImplicitAny": false, "incremental": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noImplicitThis": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./", "paths": {"@src/*": ["src/*"]}}, "include": ["src", "vitest.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}