/**
 * tenant
 * @namespace AutoGeneratedTenantTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedTenantTypes = {
    /** 组织接口接口 */
    '/wm/org/shepherd/box/org-box-org-search': {
        request: {
            wmOrgBoxAuth: {
                authCode: string;
            };
            tenantId: number;
            parentId: number;
            pageUrl: string;
            orgIds?: string;
            value?: string[];
        };
        response: {
            id: number;
            parentId: number;
            name: string;
            level: number;
            source: number;
            orgType: number;
            bizType: number;
            isLeaf: number;
            path: string;
            unreal: number;
            disable: boolean;
            childrenList?: {
                id: number;
                parentId: number;
                name: string;
                level: number;
                source: number;
                orgType: number;
                bizType: number;
                isLeaf: number;
                path: string;
                unreal: number;
                disable: boolean;
            }[];
        }[];
    };
    /** 组织结构回显 */
    '/wm/org/shepherd/box/org-box-org-tree-search': {
        request: {
            wmOrgBoxAuth: {
                authCode: string;
            };
            tenantId: number;
            parentId: number;
            pageUrl: string;
            anchorPointOrgIds: number[];
            value?: string[];
        };
        response: {
            tree: string;
            highlightOrgIds: number[];
        };
    };
    /** 获取用户业务线管理员信息 */
    '/crm/visit/platform/waimai/source/r/getUserBizManagers': {
        request: Record<string, never>;
        response: {
            teamId: number;
            teamName: string;
            managerList: {
                uid: number;
                mis: string;
                name: string;
            }[];
        }[];
    };
};
