/**
 * 任务模块
 * @namespace AutoGeneratedProductTaskTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedProductTaskTypes = {
    /** APP-查询任务列表 */
    '/bee/v1/crm/product/task/searchTaskList': {
        request: {
            pageNum: number;
            pageSize: number;
            orgIdList?: number[];
            productId?: number;
            productKeyword?: string;
            productTag?: string;
            circleStatus?: number;
            grantStatus?: number;
            createTime?: {
                startTime?: string;
                endTime?: string;
            };
            operatorUid?: number;
            /** 活动结束时间 */
            productValidEndTime?: {
                startTime?: string;
                endTime?: string;
            };
            /** 是否达标 */
            qualified?: boolean;
        };
        response: {
            row: {
                taskId: number;
                status: number;
                allStatus: number[];
                product: {
                    productId: number;
                    productName: string;
                    productType: number;
                    tagList: string[];
                    grantRange: {
                        startTime: string;
                        endTime: string;
                    };
                    validRange: {
                        startTime: string;
                        endTime: string;
                    };
                    targetPercent: number;
                    /** 站内URL */
                    receiveIMUrl: string;
                    /** 站外URL */
                    receiveWechatUrl: string;
                    /** 有奖资源文案 */
                    excitation?: string;
                };
                operator: {
                    uid: number;
                    mis: string;
                    name: string;
                };
                customTargetPercent: number;
                createTime: string;
                grantStat: {
                    grantTotal: number;
                    receiveTotal: number;
                    useTotal: number;
                    total: number;
                    receiveAmount: number;
                    useAmount: number;
                    yesterdayUseTotal: number;
                };
            }[];
            pageNum: number;
            pageSize: number;
            total: number;
        };
    };
    /** APP-查询任务详情 */
    '/bee/v1/crm/product/task/searchTaskDetail': {
        request: {
            taskId: number;
        };
        response: {
            taskId: number;
            status: number;
            allStatus: number[];
            product: {
                productId: number;
                productName: string;
                productType: number;
                tagList: string[];
                grantRange: {
                    startTime: string;
                    endTime: string;
                };
                validRange: {
                    startTime: string;
                    endTime: string;
                };
                targetPercent: number;
                /** 站内URL */
                receiveIMUrl: string;
                /** 站外URL */
                receiveWechatUrl: string;
                /** 有奖资源文案 */
                excitation?: string;
            };
            operator: {
                uid: number;
                mis: string;
                name: string;
            };
            customTargetPercent: number;
            createTime: string;
            grantStat: {
                grantTotal: number;
                receiveTotal: number;
                useTotal: number;
                total: number;
                receiveAmount: number;
                useAmount: number;
                yesterdayUseTotal: number;
            };
        };
    };
    /** PC-导出任务列表 */
    '/product/task/exportTaskList': {
        request: {
            pageNum?: number;
            pageSize?: number;
            orgIdList?: number[];
            productId?: number;
            productKeyword?: string;
            circleStatus?: number;
            grantStatus?: number;
            productTag?: string;
            createTime?: {
                startTime?: string;
                endTime?: string;
            };
            operatorUid?: number;
            productValidEndTime?: {
                startTime?: string;
                endTime?: string;
            };
            qualified?: boolean;
        };
        response: string;
    };
    /** PC-导出任务详情 */
    '/product/task/exportTaskDetail': {
        request: {
            productId: number;
            taskId: number;
            productType: number;
            grantProductStatus?: number;
        };
        response: boolean;
    };
    /** PC-提交任务详情 */
    '/product/task/submitTaskDetail': {
        request: {
            circleGrantTargetExcel?: string;
            customTargetPercent?: number;
            taskId: number;
        };
        response: {
            circleTotal: number;
            circleSuccessTotal: number;
            circleFailTotal: number;
            circleFailExcel: string;
        };
    };
    /** PC-查询任务列表 */
    '/product/task/searchTaskList': {
        request: {
            pageNum?: number;
            pageSize?: number;
            /** 组织结构 */
            orgIdList?: number[];
            /** 产品ID */
            productId?: number;
            /** 产品名 */
            productKeyword?: string;
            /** 圈选状态 */
            circleStatus?: number;
            /** 投放状态 */
            grantStatus?: number;
            /** 产品类型，充增活动 */
            productTag?: string;
            /** 创建时间 */
            createTime?: {
                startTime?: string;
                endTime?: string;
            };
            /** 执行人 */
            operatorUid?: number;
            /** 活动有效结束时间 */
            productValidEndTime?: {
                startTime?: string;
                endTime?: string;
            };
            /** 是否达标 */
            qualified?: boolean;
        };
        response: {
            row: {
                taskId: number;
                status: number;
                allStatus: number[];
                product: {
                    productId: number;
                    productName: string;
                    productType: number;
                    tagList: string[];
                    grantRange: {
                        startTime: string;
                        endTime: string;
                    };
                    validRange: {
                        startTime: string;
                        endTime: string;
                    };
                    targetPercent: number;
                    /** 站内URL */
                    receiveIMUrl: string;
                    /** 站外URL */
                    receiveWechatUrl: string;
                    /** 有奖资源文案 */
                    excitation?: string;
                };
                operator: {
                    uid: number;
                    mis: string;
                    name: string;
                };
                customTargetPercent: number;
                createTime: string;
                grantStat: {
                    grantTotal: number;
                    receiveTotal: number;
                    useTotal: number;
                    total: number;
                    receiveAmount: number;
                    useAmount: number;
                    yesterdayUseTotal: number;
                };
            }[];
            pageNum: number;
            pageSize: number;
            total: number;
        };
    };
    /** PC-查询任务详情 */
    '/product/task/searchTaskDetail': {
        request: {
            taskId: number;
        };
        response: {
            taskId: number;
            status: number;
            allStatus: number[];
            product: {
                productId: number;
                productName: string;
                productType: number;
                tagList: string[];
                grantRange: {
                    startTime: string;
                    endTime: string;
                };
                validRange: {
                    startTime: string;
                    endTime: string;
                };
                targetPercent: number;
                /** 站内URL */
                receiveIMUrl: string;
                /** 站外URL */
                receiveWechatUrl: string;
                /** 有奖资源文案 */
                excitation?: string;
            };
            operator: {
                uid: number;
                mis: string;
                name: string;
            };
            customTargetPercent: number;
            createTime: string;
            grantStat: {
                grantTotal: number;
                receiveTotal: number;
                useTotal: number;
                total: number;
                receiveAmount: number;
                useAmount: number;
                yesterdayUseTotal: number;
            };
        };
    };
};
