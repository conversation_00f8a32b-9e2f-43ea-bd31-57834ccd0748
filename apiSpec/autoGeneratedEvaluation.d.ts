/**
 * service
 * @namespace AutoGeneratedEvaluationTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedEvaluationTypes = {
    /** 导出服务详情 */
    '/impc/service/exportDetail': {
        request: {
            beginTime: number;
            endTime: number;
            orgIds: string;
            uid: string;
        };
        response: string;
    };
    /** 导出组织节点服务数据 */
    '/impc/service/exportOrgData': {
        request: {
            beginTime?: number;
            endTime?: number;
            orgIds?: string;
            misId?: string;
        };
        response: Record<string, never>;
    };
    /** 服务详情 */
    '/impc/service/getServiceDetail': {
        request: {
            beginTime?: string;
            endTime?: string;
            orgId?: string;
            misId?: string;
            serviceType?: string;
        };
        response: {
            head: {
                typeId: number;
                typeName: string;
                prop: {
                    firstOrg: string;
                    secondOrg: string;
                    thirdOrg: string;
                    callInCount: string;
                    effectiveCallRate: string;
                    bizCount: string;
                    bizResponseIn1hRate: string;
                    bizResponseIn2hRate: string;
                    bizFinishIn24hRate: string;
                    qcCount: string;
                    qcPassRate: string;
                    examScore: string;
                    attitudeViolationCount: string;
                    violationCloseBizCount: string;
                    bogusVisitCount: string;
                    bizEvaluateCount: string;
                    bizBadReviewRate: string;
                    bizGoodReviewRate: string;
                    visitEvaluateCount: string;
                    visitBadReviewRate: string;
                    visitGoodReviewRate: string;
                };
            }[];
            list: {
                callInCount: string;
                firstOrg: string;
                secondOrg: string;
                thirdOrg: string;
                effectiveCallRate: string;
                bizCount: string;
                bizResponseIn1hRate: string;
                bizResponseIn2hRate: string;
                bizFinishIn24hRate: string;
                qcCount: string;
                qcPassRate: string;
                examScore: string;
                attitudeViolationCount: string;
                violationCloseBizCount: string;
                bogusVisitCount: string;
                bizEvaluateCount: string;
                bizBadReviewRate: string;
                bizGoodReviewRate: string;
                visitEvaluateCount: string;
                visitBadReviewRate: string;
                visitGoodReviewRate: string;
            }[];
        };
    };
    /** 获取服务分列表 */
    '/impc/service/getServicePoints': {
        request: {
            beginTime?: number;
            endTime?: number;
            orgIds?: string;
            uid?: number;
        };
        response: {
            dataOverview: {
                scoreRate: string;
                rank: number;
                total: number;
                fullMarks: string;
                actualScore: string;
                serviceNum: string;
            };
            serviceItem: {
                timelinessScoreRate: number;
                averageTimelinessScoreRate: number;
                attitudeScoreRate: number;
                averageAttitudeScoreRate: number;
                majorScoreRate: number;
                averageMajorScoreRate: number;
                complaintRate: number;
                averageComplaintRate: number;
                satisfySocreRate: number;
                averageSatisfySocreRate: number;
                serviceScoreRate: number;
                averageServiceScoreRate: number;
            };
            orgDistributed: {
                head: {
                    typeId: number;
                    typeName: string;
                    prop: {
                        firstOrg: string;
                        secondOrg: string;
                        thirdOrg: string;
                        bdMis: string;
                        serviceScoreRate: string;
                        rank: string;
                        score: string;
                        fullScore: string;
                        serviceNum: string;
                        timelinessScoreRate: string;
                        attitudeScoreRate: string;
                        majorScoreRate: string;
                        complaintRate: string;
                        satisfySocreRate: string;
                    };
                }[];
                list: {
                    firstOrg: string;
                    beginTime: number;
                    endTime: number;
                    orgId: string;
                    misId: string;
                    secondOrg: string;
                    thirdOrg: string;
                    bdMis: string;
                    serviceScoreRate: string;
                    rank: string;
                    score: string;
                    fullScore: string;
                    serviceNum: string;
                    timelinessScoreRate: string;
                    attitudeScoreRate: string;
                    majorScoreRate: string;
                    complaintRate: string;
                    satisfySocreRate: string;
                }[];
            };
            marketData: {
                orgName: string;
                score: number;
            }[];
        };
    };
    /** 获取评价详情 */
    '/impc/service/getCommentDetail': {
        request: {
            beginTime?: string;
            endTime?: string;
            orgId?: string;
            misId?: string;
        };
        response: {
            org?: string;
            list?: {
                typeName: string;
                detail: {
                    commetDetail?: {
                        head?: string[];
                        commentDesc?: {
                            col6?: string;
                            col4?: string;
                            col5?: string;
                            col2?: string;
                            col3?: string;
                            col1?: string;
                        };
                        labelDesc?: {
                            col6?: string[];
                            col4?: string[];
                            col5?: string;
                            col2?: string[];
                            col3?: string;
                            col1?: string;
                        };
                    };
                    totalDesc?: string;
                    tagTotalDesc?: string;
                };
            }[];
        };
    };
};
