/**
 * 运营工作台
 * @namespace AutoGeneratedAssistantTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedAssistantTypes = {
    /** 标准问关联的扩展问 */
    '/manage/phrase/relativePhraseList': {
        request: {
            /** 标准问ID */
            id?: number;
            /** 页码，默认1 */
            pageNum?: number;
            /** 每页的数量，默认20 */
            pageSize?: number;
        };
        response: {
            /** 页码，默认1 */
            pageNum: number;
            /** 每页的数量，默认20 */
            pageSize: number;
            /** 总数 */
            total: number;
            list: {
                /** ID */
                id: number;
                /** 问法 */
                phrase: string;
                /** 状态 */
                state: string;
            }[];
        };
    };
    /** 获取下载模板 */
    '/manage/xxx': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 批量添加-模版下载 */
    '/manage/phrase/standard/batch/downloadTemplate': {
        request: Record<string, never>;
        response: {
            fileUrl?: string;
        };
    };
    /** 获取下载模板 */
    '/manage/xxx': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 批量添加-表格上传 */
    '/manage/phrase/standard/batch/upload': {
        request: Record<string, never>;
        response: {
            /** 对应预添加数据表，用于分页查询 */
            processId: number;
        };
    };
    /** 批量添加-分页请求预添加数据 */
    '/manage/phrase/standard/batch/list': {
        request: {
            /** 预添加过程id,默认最近一次有效过程id */
            id: number;
            /** 页码，默认1 */
            pageNum?: number;
            /** 每页的数量，默认20 */
            pageSize?: number;
        };
        response: {
            /** 页码，默认1	 */
            pageNum?: number;
            /** 每页的数量，默认20	 */
            pageSize?: number;
            /** 总数 */
            total?: number;
            /** 预添加过程Id */
            processId: number;
            /** 状态；init：初始化；imported：导入完成；recorded：执行完成； */
            status: string;
            list?: {
                /** 预添加FaqId */
                id?: number;
                /** 域id */
                domainId?: number;
                /** 预添加问题 */
                question?: string;
                /** 预添加答案(json) */
                answer?: string;
                /** 预添加tt链接 */
                ttUrl?: string;
                /** 相似问题id */
                phraseId?: number;
                /** 相似问题 */
                similarQuestion?: string;
                /** 相似答案 */
                similarAnswer?: string;
                /** 是否替换相似问题？,0否，1是 */
                replace?: number;
            }[];
        };
    };
    /** 批量添加-修改 */
    '/manage/phrase/standard/batch/edit': {
        request: {
            /** 预添加FaqId */
            id?: number;
            /** 领域Id */
            domainId?: number;
            /** 预添加问题 */
            question?: string;
            /** 预添加答案 */
            answer?: string;
            /** 预添加tt链接 */
            ttUrl?: string;
        };
        response: Record<string, never>;
    };
    /** 批量添加-相似替换 */
    '/manage/phrase/standard/batch/similarReplace': {
        request: {
            /** 预添加FaqId */
            id?: number;
            /** 是否替换相似问题？,0否，1是 */
            replace?: number;
        };
        response: Record<string, never>;
    };
    /** 批量添加-删除 */
    '/manage/phrase/standard/batch/delete': {
        request: {
            /** 预添加FaqId */
            id?: number;
        };
        response: Record<string, never>;
    };
    /** 批量添加-批量录入（最终） */
    '/manage/phrase/standard/batch/executeImport': {
        request: {
            /** 预添加过程Id */
            processId: number;
        };
        response: {
            /** 成功插入的消息 */
            message?: string;
        };
    };
    /** 获取下载模板 */
    '/manage/xxx': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 批量添加-回滚 */
    '/manage/phrase/standard/batch/rollbackImport': {
        request: {
            /** 预添加过程Id */
            processId?: number;
        };
        response: {
            /** 回滚成功数量 */
            count?: number;
        };
    };
    /** 领域-领域列表 */
    '/manage/domain/list': {
        request: Record<string, never>;
        response: {
            domainList: {
                /** 域id */
                id: string;
                /** 领域：例如，资质驳回，通用域 */
                domain: string;
            }[];
        };
    };
    /** 关键词列表 */
    '/manage/keyword/list': {
        request: {
            /** 关键词 */
            keyword?: string;
            /** 分页参数，页码，默认1 */
            pageNum?: string;
            /** 分页参数，每页大小，默认20 */
            pageSize?: string;
        };
        response: {
            /** 分页参数，页码，默认1 */
            pageNum: number;
            /** 分页参数，每页大小，默认20 */
            pageSize: number;
            /** 总数 */
            total: number;
            /** 关键词列表 */
            list: {
                /** ID */
                id: string;
                /** 领域：通用域、资质驳回 */
                domain: string;
                /** 关键词 */
                keyword: string;
                /** 描述 */
                description: string;
                /** 更新时间 */
                utime: string;
                /** 修改人名称 */
                modifierName: string;
                /** 修改人mis */
                modifierMis: string;
            }[];
        };
    };
    /** 关键词新增 */
    '/manage/keyword/add': {
        request: {
            /** 关键词 */
            keyword: string;
            /** 关键词描述 */
            description: string;
            /** 领域Id */
            domainId: number;
        };
        response: Record<string, never>;
    };
    /** 关键词修改 */
    '/manage/keyword/edit': {
        request: {
            /** 关键词 */
            keyword: string;
            /** 关键词描述 */
            description: string;
            /** 领域id */
            domainId: number;
            /** 关键词id */
            id: number;
        };
        response: Record<string, never>;
    };
    /** 关键词删除 */
    '/manage/keyword/delete': {
        request: {
            /** 关键词id */
            id: number;
        };
        response: Record<string, never>;
    };
    /** 标准问上线 */
    '/manage/phrase/standard/online': {
        request: {
            /** 标准问ID */
            id: number;
        };
        response: Record<string, never>;
    };
    /** 标准问下线 */
    '/manage/phrase/standard/offline': {
        request: {
            /** 标准问ID */
            id: number;
        };
        response: Record<string, never>;
    };
    /** 会话列表 */
    '/manage/session/list': {
        request: {
            /** mis */
            mis?: string;
            /** 日期，格式为 yyyy-MM-dd */
            startDate: string;
            /** 日期，格式为 yyyy-MM-dd */
            endDate: string;
            /** 会话ID */
            sessionId?: number;
            /** 标准问ID或者标准问名称 */
            phraseData?: string;
            /** 页号，从1开始 */
            pageNum?: number;
            /** 分页参数，每页个数 */
            pageSize?: number;
            /** 是否已转TT */
            submittedTt?: boolean;
            /** 是否备注过 */
            marked?: boolean;
            /** 域Id */
            domainId?: string;
        };
        response: {
            sessionList: {
                /** 会话ID */
                id: string;
                /** mis */
                mis: string;
                /** 中文名称 */
                name: string;
                /** 组织架构（可能有多个） */
                orgInfos: string[];
                /** 会话创建时间，格式为yyyy-MM-dd HH:mm:ss */
                createTime: string;
                /** 是否已转TT */
                submittedTt: boolean;
                /** 是否备注过 */
                marked: boolean;
                /** 标准问名称列表 */
                phraseNameList: string[];
                domainList: string[];
            }[];
            /** 页号，从1开始 */
            pageNum: number;
            /** 每页大小 */
            pageSize: number;
            /** 总数 */
            total: number;
        };
    };
    /** 会话详情-分页查询会话 */
    '/manage/session/conversations': {
        request: {
            /** 分页参数；默认1； */
            pageNum: number;
            /** 分页参数；默认20； */
            pageSize: number;
            /** 会话ID */
            sessionId: number;
        };
        response: {
            /** 分页参数；默认1； */
            pageNum: number;
            /** 分页参数；默认20； */
            pageSize: number;
            /** 中文名称 */
            total: number;
            conversations: {
                /** 消息类型；qustion：用户问题；answer：系统回复； */
                conversationType: string;
                /** 内容 */
                currentContent: string;
                /** 前缀内容 */
                prefixTextContent: string;
                /** 后缀内容 */
                postTextContent: string;
                /** 图片列表 */
                imageList: string[];
                /** 选项列表 */
                selectionItems: {}[];
            }[];
        };
    };
    /** 会话详情-基本信息 */
    '/manage/session/detail': {
        request: {
            /** 会话ID */
            sessionId: string;
        };
        response: {
            /** 会话ID */
            id: string;
            /** 会话人mis */
            mis: string;
            /** 中文名称 */
            name: string;
            /** 会话创建时间，格式为yyyy-MM-dd HH:mm:ss */
            createTime: string;
            /** 组织架构（可能有多个） */
            orgInfos: string[];
            /** 是否提交过TT，true：提交过；false：未提交过 */
            submittedTt: boolean;
            /** 备注内容 */
            remark: string;
            /** 标准问列表 */
            phraseNameList: string[];
            domainList: string[];
        };
    };
    /** 会话评论 */
    '/manage/session/comment': {
        request: {
            /** 会话ID */
            sessionId: number;
            /** 评论 */
            comment: string;
        };
        response: Record<string, never>;
    };
    /** 测试链接生成 */
    '/manage/phrase/standard/testUrl': {
        request: {
            /** 标准问ID */
            id: number;
        };
        response: string;
    };
    /** 扩展问新增 */
    '/manage/phrase/add': {
        request: {
            /** ID */
            id: number;
            /** 扩展问 */
            phrase: string;
        };
        response: Record<string, never>;
    };
    /** 扩展问修改 */
    '/manage/phrase/modify': {
        request: {
            /** 标准问 */
            id: string;
            /** 扩展问 */
            phrase: string;
        };
        response: Record<string, never>;
    };
    /** 扩展问删除 */
    '/manage/phrase/delete': {
        request: {
            /** 标准问 */
            id: string;
        };
        response: Record<string, never>;
    };
    /** 扩展问导出 */
    '/manage/phrase/export': {
        request: Record<string, never>;
        response: string;
    };
    /** 标准问新增 */
    '/manage/phrase/standard/add': {
        request: {
            /** 这是一个标准问问题 */
            question: string;
            /** 问题回答(可以是富文本，图片格式”![picture|图片描述](图片链接)“) */
            answer: string;
            /** TT链接 */
            ttUrl: string;
            /** faq：标准问；task：多轮。 */
            type: string;
            /** 领域：通用域、资质驳回 */
            domainId: number;
        };
        response: Record<string, never>;
    };
    /** 标准问详情 */
    '/manage/phrase/standard/detail': {
        request: {
            /** ID */
            id: string;
            /** 类型。faq: 问答类；task : 任务流； */
            type: string;
        };
        response: {
            /** ID */
            id: number;
            /** 类型。faq: 问答类；task : 任务流； */
            type: string;
            /** 领域Id */
            domainId: number;
            /** 问题 */
            question: string;
            /** 回答 */
            answer: string;
            /** TT链接 */
            ttUrl: string;
        };
    };
    /** 标准问修改 */
    '/manage/phrase/standard/modify': {
        request: {
            /** 标准问ID */
            id: number;
            /** 标准问问题 */
            question: string;
            /** 问题回答(可以是富文本，图片格式”![picture|图片描述](图片链接)“) */
            answer: string;
            /** TT链接 */
            ttUrl: string;
            /** faq: 问答类；task : 任务流； */
            type: string;
            /** 领域id */
            domainId: number;
        };
        response: Record<string, never>;
    };
    /** 标准问删除 */
    '/manage/phrase/standard/delete': {
        request: {
            /** ID */
            id: string;
            /** 类型。faq：标准问，task ：多轮。 */
            type: string;
        };
        response: Record<string, never>;
    };
    /** 标准问导出 */
    '/manage/phrase/standard/export': {
        request: Record<string, never>;
        response: string;
    };
    /** 标准问列表 */
    '/manage/phrase/standard/list': {
        request: {
            /** 标准问关键字 */
            name?: string;
            /** faq: 问答类；task : 任务流； */
            type?: string;
            /** 分页参数，页码，默认1 */
            pageNum?: string;
            /** 分页参数，每页大小，默认20 */
            pageSize?: string;
            /** 是否需要扩展问个数。true：需要；false：不需要； */
            needExtendSize?: string;
            /** 领域id */
            domainId?: string;
        };
        response: {
            /** 分页参数，页码，默认1 */
            pageNum: number;
            /** 分页参数，每页大小，默认20 */
            pageSize: number;
            /** 总数 */
            total: number;
            /** 标准问列表 */
            list: {
                /** ID */
                id: string;
                /** 领域Id */
                domainId: number;
                /** 问题 */
                question: string;
                /** faq: 问答类；task : 任务流； */
                type: string;
                /** 更新时间 */
                utime: string;
                /** 修改人名称 */
                modifierName: string;
                /** 修改人mis */
                modifierMis: string;
                /** 预览内容 */
                preview: string;
                /** 触发器ID，与type联动，当 type = task，此ID为多轮ID，当 type = faq，此ID为单轮ID。 */
                triggerId: number;
                /** 扩展问个数 */
                phraseSize: number;
                /** 状态 */
                state: string;
            }[];
        };
    };
    /** 获取s3加签信息 */
    '/common/s3': {
        request: Record<string, never>;
        response: {
            AWSAccessKeyId?: string;
            policy?: string;
            signature?: string;
            key?: string;
        };
    };
    /** 权限列表 */
    '/manage/common/permissions': {
        request: Record<string, never>;
        response: string[];
    };
};
