import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const usePermissions = () => {
    return useRequest(
        async () => {
            const res = await apiCaller.get('/manage/common/permissions', {});
            if (res.code !== 0) {
                return [] as 'phrase_data_export'[];
            }
            return res.data as 'phrase_data_export'[];
        },
        { cacheKey: '/manage/common/permissions' },
    );
};
export default usePermissions;
