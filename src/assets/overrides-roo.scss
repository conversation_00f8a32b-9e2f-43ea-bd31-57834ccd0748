input.roo-input,
div.roo-input,
div.roo-input-group.has-icon .roo-input:only-of-type {
    &:hover:not([readonly]):not(.readonly):not(.readOnly):not([disabled]):not(
            .disabled
        ) {
        border-color: var(--colorPrimaryHover);
    }
    border-radius: var(--borderRadius);
    height: var(--controlHeight);
    border-color: var(--colorBorder);
}

button.roo-btn {
    border-radius: var(--borderRadius);

    &.roo-btn-default:hover {
        border-color: var(--colorPrimaryHover);
        color: var(--colorPrimaryHover);
    }

    &.roo-btn-primary {
        background-color: var(--colorPrimary);
        border-color: var(--colorPrimaryBorder);
        color: #222;

        &:hover {
            color: #222;
            border-color: var(--colorPrimaryHover);
            background-color: var(--colorPrimaryHover);
        }
    }
}

.roo-checkbox input:checked + span.custom-checkbox {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimaryBorder) !important;
}

.roo-checkbox span.half-checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimaryBorder) !important;
}

div.roo-plus-org-panel.roo-panel .roo-panel-footer {
    padding: 0;
}

div.roo-plus-org-panel.roo-panel .roo-panel-body {
    padding: 0 20px 20px 20px;
}
