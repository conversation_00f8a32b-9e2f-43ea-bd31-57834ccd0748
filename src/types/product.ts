import { PickerWithTypeValue } from '@src/components/PickerWithType';
import { formatProductItem } from '@src/pages/product/list/utils';

export const enum ProductSource {
    商增 = 1,
}

export const enum ProductType {
    CM = 1,
    BD,
}

export const enum ProductTag {
    直接发券 = '直接发券',
    充值满赠 = '充值满赠',
    消耗满赠 = '消耗满赠',
    有奖资源 = '有奖资源',
}

// 计算状态，仅提供搜索, 其他地方请使用ProductStatus
export const enum ProductSelectionStatus {
    不需要圈选,
    待圈选,
    已圈选,
}

// 计算状态，仅提供搜索，其他地方请使用ProductStatus
export const enum ProductDispatchStatus {
    待下发 = 1,
    已下发,
    已过期,
}

export const enum ProductStatus {
    不需要圈选 = -1, // 前端自定义状态，实际在后端不存在
    待圈选 = 1,
    已圈选,
    待下发,
    已下发,
    已过期,
}

export const enum ProductQualifiedStatus {
    NOT_MEET = 0,
    MEET = 1,
}

export const enum ProductGrantStatus {
    待领取 = 1,
    已领取,
    已使用,
    已过期,
}

export type FormatedProductItem = ReturnType<typeof formatProductItem>;

export interface ProductListSearchFormData {
    dateRange?: PickerWithTypeValue;
    selectionStatusWithProductType: {
        circleStatus: number;
        productType: number;
    };
    taskStatus: number;
    grantStatus: number;
    qualified?: number;
    orgIdList?: number[];
}
