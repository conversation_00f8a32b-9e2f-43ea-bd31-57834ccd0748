import { SSOGuard } from '@mtfe/sso-web';

const initSso = () => {
    try {
        const ssoGuard = new SSOGuard({
            clientId: 'xianfu_waimai', // 项目接入sso的clientId,注意区分线上下线
            accessEnv: ['staging', 'production'].includes(import.meta.env.VITE_DEPLOY_ENV) ? 'product' : 'test', // 环境 线下:test 线上:product。 staging环境使用product, 本地开发用test。
            appkey: import.meta.env.VITE_APPKEY, // 项目的appkey
        });
        ssoGuard.init();
    } catch (e) {
        console.log(e);
    }
};
export default initSso;
