import React, { ReactNode } from 'react';

export default function ({ children }) {
    return <div>{children}</div>;
}
export function Button({ onClick }: { onClick: any }) {
    return <div onClick={onClick}>123</div>;
}
export function Tooltip({ children }) {
    return <div>{children}</div>;
}
export function Loading({ children }) {
    return <div>{children}</div>;
}
export class Toast {
    static fail({ children }: { children: string }) {
        return 1;
    }
}
export function TableNew(params: any) {
    const { columns, data } = params;
    const testData = data[0];
    console.log(columns, data);

    const returns = columns.map(column => {
        if (column.render) {
            return column.render(testData[column.dataIndex]);
        }
        return <span key={testData[column.dataIndex]}>testData[column.dataIndex]</span>;
    });
    return <div>{returns} </div>;
}
export function Pagination(params: any) {
    const { children } = params;
    return <div>{children}</div>;
}
export function Modal(params: any) {
    return <div>alskjdalksdj</div>;
}
export function Input(params: any) {
    return <div>123123</div>;
}
export function Switch(params: any) {
    return <div>askldjdlka</div>;
}
export function Icon(params: any) {
    return <div>asdasdasd</div>;
}

export function DropMenu(params: any) {
    return <div>asd</div>;
}
function DropDownList({ children }: { children: ReactNode }) {
    return <div>{children}</div>;
}

DropMenu.List = DropDownList;

export type BrandCheckBoxProps = any;
export function CheckBox(params: BrandCheckBoxProps) {
    return <div>asdasd</div>;
}

export function Radio(params: any) {
    return <div>Asdlkjasd</div>;
}
Radio.Group = function (params: any) {
    return <div>asdasd</div>;
};

export function Drawer(params: any) {
    return <div>Asdasd</div>;
}

export function Tag(params: any) {
    return <div>asdlkjas</div>;
}
export function DatePicker(params: any) {
    return <div>Asdasd</div>;
}
DatePicker.RangePicker = function (params: any) {
    return <div>asdasd</div>;
};
function Tabs(params: any) {
    return <div>aslkdjasa</div>;
}

function Pane(params: any) {
    return <div>asdlkjasd</div>;
}

Tabs.Pane = Pane;

export { Tabs };

// apiCaller
export const apiCaller = {
    send: (url, obj) => Promise.resolve(1),
    get: (url, obj) => Promise.resolve(1),
    xSend: (url, obj) => Promise.resolve(1),
};

// i18n
export const i18nClient = {
    t: () => 'i18n text',
};

export const isOverseaBiz = () => false;
export const getLanguage = () => 'zh';
export const getProjectType = () => '';

export const useLX = () => () => {
    console.log(123);
};
