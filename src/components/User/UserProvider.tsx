import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import { PropsWithChildren, createContext, useContext } from 'react';

type User = APISpec['/uicomponent/getLoginUser']['response'];

const userContext = createContext({
    user: undefined as User | undefined,
});

export const useUserContext = () => useContext(userContext);

const UserProvider = (props: PropsWithChildren<any>) => {
    const fetchUser = async () => {
        const res = await apiCaller.get('/uicomponent/getLoginUser', {});
        if (res.code !== 0) {
            return;
        }

        return res.data;
    };

    const { data } = useRequest(fetchUser);

    if (!data) {
        return <Spin spinning />;
    }

    return <userContext.Provider value={{ user: data }}>{props.children}</userContext.Provider>;
};

export default UserProvider;
