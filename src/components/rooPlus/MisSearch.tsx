import RooPlusMisSearch from '@roo/roo-plus/MisSearch';
import { MisSearchProps } from '@roo/roo-plus/MisSearch/interface';

const MisSearch = (props: MisSearchProps) => (
    <RooPlusMisSearch
        url="/xianfu/api/common/uicomponent/employs"
        handleResponseData={res => {
            if (props.url?.endsWith('/uicomponent/employs') && res.code === 1) {
                return res.data;
            }
            return res.data;
        }}
        placeholder="请输入姓名/Mis搜索"
        {...props}
    />
);

export default MisSearch;
