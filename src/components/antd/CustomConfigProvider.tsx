import 'antd/dist/reset.css';
import 'dayjs/locale/zh-cn';

import { App, ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN'; // vite 不支持 es之外的，所以要用es或者esm目录下的locale包
import OverridesRoo from '../rooPlus/OverridesRoo';
import '@src/assets/global.scss';

const CustomConfigProvider = ({ children }) => {
    return (
        <ConfigProvider
            locale={zhCN}
            theme={{
                token: {
                    colorPrimary: '#FFcc33',
                    colorLink: '#FF6A00',
                },
                components: {
                    Button: {
                        colorTextLightSolid: '#222222',
                    },
                },
            }}
        >
            <App>
                <OverridesRoo>{children}</OverridesRoo>
            </App>
        </ConfigProvider>
    );
};

export default CustomConfigProvider;
