import { But<PERSON>, DatePicker, Form, TimeRangePickerProps, Typography } from 'antd';
import AdaptiveGrid from '../AdaptiveGrid';
import dayjs, { Dayjs } from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import OrganizationSelector from '../rooPlus/OrganizationSelector';
import MisSearch from '../rooPlus/MisSearch';
import { useContext } from 'react';
import VersionContext, { defaultVersion, Version } from '@src/pages/evaluation/context/version';
import { useRequest } from 'ahooks';

type Employ = APISpec['/uicomponent/employs']['response'][number];
type SearchParams = APISpec['/impc/service/getServicePoints']['request'] & { version?: Version };

interface FormData {
    range?: Dayjs[];
    orgIds?: number | number[];
    bd?: Employ;
}

interface EvaluationSearchForm {
    onSearch: (p: SearchParams) => void;
}

const EvaluationSearchForm = (props: EvaluationSearchForm) => {
    const [form] = Form.useForm();

    const onFinish = (data: FormData) => {
        const formData = { ...form.getFieldsValue(), ...data };
        const [start, end] = formData.range || [];

        const params: SearchParams = {
            beginTime: start?.startOf('d').unix(),
            endTime: end?.endOf('d').unix(),
            uid: formData.bd?.id,
            orgIds: formData?.orgId ? `${formData?.orgId}` : undefined,
            version: formData.version,
        };

        props.onSearch(params);
    };

    const onReset = () => {
        form.resetFields();

        props.onSearch({});
    };

    const rangePresets: TimeRangePickerProps['presets'] = [
        { label: '本月', value: [dayjs().startOf('M'), dayjs().add(-1, 'd')] },
        {
            label: '上月',
            value: [dayjs().add(-1, 'M').startOf('M'), dayjs().add(-1, 'M').endOf('M')],
        },
    ];

    const { setVersion } = useContext(VersionContext);

    const { data: isInGray } = useRequest(async () => {
        // @ts-ignore
        const res = await apiCaller.get('/impc/service/web/judgeUserInGray', {});
        if (res.code === 0) {
            return res.data;
        }
    }, {});

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={{
                version: defaultVersion,
                range: [dayjs().add(-2, 'd').startOf('M'), dayjs().add(-2, 'd')],
            }}
        >
            <AdaptiveGrid divide={4}>
                <Form.Item name="range" label="选时间段">
                    <DatePicker.RangePicker
                        presets={rangePresets}
                        disabledDate={d => d.diff(dayjs(), 'd') >= 0}
                        style={{ width: '100%' }}
                        allowClear={false}
                    />
                </Form.Item>

                <Form.Item name="orgId" label="选组织架构">
                    <OrganizationSelector
                        params={{
                            backtrackOrgType: 3,
                            sources: '1_4_5_6_10_11_12_13_16_17_18_23',
                        }}
                        onInit={ids => {
                            form.setFieldsValue({ orgId: ids });
                            onFinish({ orgIds: ids });
                        }}
                        disablePortal
                    />
                </Form.Item>

                <Form.Item name="bd" label="BD Mis">
                    <MisSearch />
                </Form.Item>
            </AdaptiveGrid>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography.Text type="danger">数据建议T-2查询，举例：11月17日查，11月1日-11月15日数据</Typography.Text>
                <Form.Item>
                    <Button style={{ marginRight: 10 }} onClick={onReset}>
                        重置
                    </Button>
                    <Button htmlType="submit" type="primary">
                        查询
                    </Button>
                </Form.Item>
            </div>
        </Form>
    );
};

export default EvaluationSearchForm;
