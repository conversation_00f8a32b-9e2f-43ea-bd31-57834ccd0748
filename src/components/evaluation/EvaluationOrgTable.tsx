import { APISpec } from '@mfe/cc-api-caller-pc';
import Title from '../Title';
import { Button, Table, TableColumnType, Typography, message } from 'antd';
import { useDebounceFn } from 'ahooks';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { Excel } from 'antd-table-saveas-excel';
import dayjs from 'dayjs';
import { objectToQuery } from '@src/utils/query';
import { UploadOutlined } from '@ant-design/icons';
import { useContext } from 'react';
import VersionContext from '@src/pages/evaluation/context/version';

type OrgData = APISpec['/impc/service/getServicePoints']['response']['orgDistributed'];

interface EvaluationOrgTable {
    data?: OrgData;
    tracker: any;
}

const enum TableHeadType {
    PRIMARY = 1,
    PRIMARY_FRACTION,
    SECONDARY,
}

const EvaluationOrgTable = (props: EvaluationOrgTable) => {
    const { data } = props;

    if (!data) {
        return null;
    }

    const { version } = useContext(VersionContext);
    const gotoDetail = (serviceType, name, record) => {
        const params = {
            beginTime: record.beginTime,
            endTime: record.endTime,
            uid: record.uid,
            misId: record.misId,
            orgId: record.orgId,

            serviceType,
            name,

            version, // 版本参数，控制是服务分1.0还是服务分2.0
        };

        window.open(bellwetherLinkParse(`./detail${objectToQuery(params)}`));
    };

    /* eslint-disable @typescript-eslint/indent */
    const columns: TableColumnType<OrgData['list'][number]>[] = data.head.map(item => ({
        title: item.typeName,
        className: `evaluation-block__org__head--${item.typeId}`,
        children: Object.keys(item.prop).map(key => ({
            dataIndex: key,
            title: item.prop[key],
            className: `evaluation-block__org__head--${item.typeId}`,
            // fixed: key === 'org' ? 'left' : undefined,
            render:
                item.typeId === TableHeadType.SECONDARY
                    ? (text, record) => (
                          <Button type="link" onClick={() => gotoDetail(key, item.prop[key], record)}>
                              {String(text || 0)}
                          </Button>
                      )
                    : undefined,
        })),
    }));
    /* eslint-enable @typescript-eslint/indent */

    const localExport = () => {
        props.tracker(
            'moduleClick',
            // valBid string moduleView 命令发送的统计数据中，事件体 val_bid 字段的值
            'b_waimai_m_7umqrf1q_mc',
            // valLab objectmoduleClick 命令发送的统计数据中，事件体 val_lab 字段的值
            null,
        );

        const excel = new Excel();
        const timeColumn = {
            title: '数据展示时间',
            render: (_, record) => {
                const start = dayjs.unix(record.beginTime).format('YYYY/MM/DD');
                const end = dayjs.unix(record.endTime).format('YYYY/MM/DD');
                return `${start}-${end}`;
            },
        };

        try {
            excel
                .addSheet('test')
                // @ts-ignore
                .addColumns([...columns, timeColumn])
                .addDataSource(data.list)
                .saveAs(`服务分${dayjs().format('YYYYMMDDHHmmss')}.xlsx`);
        } catch (e) {
            console.log(e);
            message.error('导出失败，请刷新页面重试');
        }
    };

    const { run } = useDebounceFn(localExport, { wait: 300 });

    return (
        <div className="evaluation-block">
            <Title>按同级及下级组织结构分布</Title>

            <div className="evaluation-block__org-export">
                <Typography.Text type="danger" style={{ fontSize: 15 }}>
                    点击下方橙色字体↓直接看明细！无需导出！！！
                </Typography.Text>
                <Button onClick={run} type="link" icon={<UploadOutlined />}>
                    导出
                </Button>
            </div>
            <Table columns={columns} dataSource={data.list} scroll={{ x: 1500 }} pagination={false} bordered />
        </div>
    );
};

export default EvaluationOrgTable;
