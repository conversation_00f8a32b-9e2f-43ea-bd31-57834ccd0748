import { Button, Form, Input, InputNumber, Select, Space, Tooltip, Typography } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
    },
};

const formItemLayoutWithOutLabel = {
    wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 },
    },
};

const Demo = () => {
    const [form] = Form.useForm<{ name: string; age: number }>();
    const nameValue = Form.useWatch('name', form);

    return (
        <>
            <Form form={form} {...formItemLayout} autoComplete="off" style={{ maxWidth: 800 }} onFinish={console.log}>
                <Form.Item name="name" label="Name (Watch)">
                    <Input />
                </Form.Item>
                <Form.Item label="Username">
                    <Space>
                        <Form.Item
                            name="nickname"
                            noStyle
                            rules={[
                                {
                                    required: true,
                                    message: 'nickname is required',
                                },
                            ]}
                        >
                            <Input style={{ width: 160 }} placeholder="Please input" />
                        </Form.Item>
                        <Tooltip title="Useful information">
                            <Typography.Link href="https://www.baidu.com" target="_blank">
                                Need Help?
                            </Typography.Link>
                        </Tooltip>
                    </Space>
                </Form.Item>
                <Form.Item name="age" label="Age (Not Watch)">
                    <InputNumber />
                </Form.Item>
                <Form.Item label="Address">
                    <Space.Compact>
                        <Form.Item
                            name={['address', 'province']}
                            noStyle
                            rules={[
                                {
                                    required: true,
                                    message: 'Province is required',
                                },
                            ]}
                        >
                            <Select placeholder="Select province">
                                <Select.Option value="Zhejiang">Zhejiang</Select.Option>
                                <Select.Option value="Jiangsu">Jiangsu</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            name={['address', 'street']}
                            noStyle
                            rules={[
                                {
                                    required: true,
                                    message: 'Street is required',
                                },
                            ]}
                        >
                            <Input style={{ width: '50%' }} placeholder="Input street" />
                        </Form.Item>
                    </Space.Compact>
                </Form.Item>

                <Form.Item name={['grade', 'math']} label="数学成绩（嵌套）">
                    <InputNumber addonAfter={<span>分</span>} />
                </Form.Item>

                <Form.List
                    name="names"
                    rules={[
                        {
                            validator: async (_, names) => {
                                if (!names || names.length < 2) {
                                    return Promise.reject(new Error('At least 2 passengers'));
                                }
                            },
                        },
                    ]}
                >
                    {(fields, { add, remove }, { errors }) => (
                        <>
                            {fields.map((field, index) => (
                                <Form.Item
                                    {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                                    label={index === 0 ? 'Passengers' : ''}
                                    required={false}
                                    key={field.key}
                                >
                                    <Form.Item
                                        {...field}
                                        validateTrigger={['onChange', 'onBlur']}
                                        rules={[
                                            {
                                                required: true,
                                                whitespace: true,
                                                message: "Please input passenger's name or delete this field.",
                                            },
                                        ]}
                                        noStyle
                                    >
                                        <Input placeholder="passenger name" style={{ width: '60%' }} />
                                    </Form.Item>
                                    {fields.length > 1 ? (
                                        <MinusCircleOutlined
                                            className="dynamic-delete-button"
                                            onClick={() => remove(field.name)}
                                        />
                                    ) : null}
                                </Form.Item>
                            ))}
                            <Form.Item>
                                <Button
                                    type="dashed"
                                    onClick={() => add()}
                                    style={{ width: '60%' }}
                                    icon={<PlusOutlined />}
                                >
                                    Add field
                                </Button>
                                <Button
                                    type="dashed"
                                    onClick={() => {
                                        add('The head item', 0);
                                    }}
                                    style={{ width: '60%', marginTop: '20px' }}
                                    icon={<PlusOutlined />}
                                >
                                    Add field at head
                                </Button>
                                <Form.ErrorList errors={errors} />
                            </Form.Item>
                        </>
                    )}
                </Form.List>
                <Form.Item wrapperCol={{ ...formItemLayout.wrapperCol, offset: 12 }}>
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item>
            </Form>

            <Typography>
                <pre>Name Value: {nameValue}</pre>
            </Typography>
        </>
    );
};

export default Demo;
