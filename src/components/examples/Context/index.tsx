import { Button, Checkbox, Form, Input, List, Modal } from 'antd';
import { useState } from 'react';
import { useTodoContext } from './Provider';

const TodoList = () => {
    const { todos, saveTodo, updateTodo } = useTodoContext();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    return (
        <div>
            <Button
                type="primary"
                onClick={() => {
                    setOpen(true);
                }}
            >
                Add Todo
            </Button>
            <List
                itemLayout="horizontal"
                dataSource={todos}
                renderItem={item => (
                    <List.Item>
                        <List.Item.Meta
                            title={
                                <>
                                    <Checkbox checked={item.status} onChange={() => updateTodo(item.id)} />
                                    <span style={{ marginLeft: 10 }}>{item.title}</span>
                                </>
                            }
                            description={item.description}
                        />
                    </List.Item>
                )}
            />

            <Modal
                open={open}
                title="Add a new Todo"
                okText="Add"
                cancelText="Cancel"
                onCancel={() => setOpen(false)}
                onOk={() => {
                    form.validateFields()
                        .then(values => {
                            form.resetFields();
                            saveTodo(values);
                        })
                        .catch(info => {
                            console.log('Validate Failed:', info);
                        });
                }}
            >
                <Form form={form} layout="vertical" name="form_in_modal" initialValues={{ modifier: 'public' }}>
                    <Form.Item
                        name="title"
                        label="Title"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the title of todo!',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item name="description" label="Description">
                        <Input type="textarea" />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default TodoList;
