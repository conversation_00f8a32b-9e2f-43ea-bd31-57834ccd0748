import { createContext, PropsWithChildren, useContext, useState } from 'react';

export interface Todo {
    id: number;
    title: string;
    description: string;
    status: boolean;
}
export type TodoContextType = {
    todos: Todo[];
    saveTodo: (todo: Todo) => void;
    updateTodo: (id: number) => void;
};

const createDefault = () => ({
    todos: [
        {
            id: 1,
            title: '约会',
            description: '明天下午要开个会',
            status: false,
        },
    ],
    saveTodo: () => {},
    updateTodo: () => {},
});

const TodoContext = createContext<TodoContextType>(createDefault());

export const useTodoContext = () => useContext(TodoContext);

const TodoProvider = ({ children }: PropsWithChildren) => {
    const [todos, setTodos] = useState<Todo[]>(createDefault().todos);

    const saveTodo = (todo: Todo) => {
        const newTodo: Todo = {
            id: Math.random(), // not really unique - but fine for this example
            title: todo.title,
            description: todo.description,
            status: false,
        };
        setTodos([...todos, newTodo]);
    };

    const updateTodo = (id: number) => {
        todos.filter((todo: Todo) => {
            if (todo.id === id) {
                todo.status = !todo.status;
                setTodos([...todos]);
            }
        });
    };

    return <TodoContext.Provider value={{ todos, saveTodo, updateTodo }}>{children}</TodoContext.Provider>;
};

export default TodoProvider;
