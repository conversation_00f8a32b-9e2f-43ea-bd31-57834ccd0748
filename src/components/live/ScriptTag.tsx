import { Tag } from 'antd';
import React from 'react';
import { TaskStatus } from './types';

interface ScriptTag {
    status?: number;
    style?: React.CSSProperties;
}

const StatusMap = new Map<number, { text: string; color: string }>([
    [TaskStatus.NOT_STARTED, { text: '未开始', color: 'blue' }],
    [TaskStatus.DOING, { text: '生成中', color: 'orange' }],
    [TaskStatus.FAILED, { text: '生成失败', color: 'red' }],
    [TaskStatus.SUCCESS, { text: '生成成功', color: 'green' }],
    [TaskStatus.SUCCESS_VIEWED, { text: '生成成功', color: 'green' }],
]);

const ScriptTag = (props: ScriptTag) => {
    if (!props.status) {
        return null;
    }

    const config = StatusMap.get(props.status);

    if (!config) {
        return null;
    }

    return (
        <Tag style={props.style} color={config.color}>
            {config.text}
        </Tag>
    );
};

export default ScriptTag;
