import { CloseOutlined } from '@ant-design/icons';
import { useLocalStorageState } from 'ahooks';
import { Flex, Popover, Typography } from 'antd';
import { PropsWithChildren, useEffect, useState } from 'react';

export interface ProductSelectButtonPopoverProps {
    shouldPop: boolean;
}

const ProductSelectButtonPopover = (props: PropsWithChildren<ProductSelectButtonPopoverProps>) => {
    const [popoverOpen, setOpen] = useState(props.shouldPop);
    const [veteran, setVeteran] = useLocalStorageState<number | undefined>(
        'use-local-storage-product-selection-popover',
    );

    useEffect(() => {
        return () => {
            setVeteran(1);
        };
    }, [popoverOpen]);

    return (
        <Popover
            open={!veteran && popoverOpen}
            placement="bottomRight"
            title={
                <Flex justify="space-between">
                    <Typography.Text style={{ fontSize: 18 }}>CM圈选商家</Typography.Text>
                    <CloseOutlined style={{ fontSize: 12, color: '#666' }} onClick={() => setOpen(false)} />
                </Flex>
            }
            content={
                <div style={{ width: 325, marginTop: 10 }}>
                    「CM下发」资源需要您圈商家，圈选后BD接到下发任务。「BD下发」资源不需要您圈选，BD可以直接下发。
                </div>
            }
            color="#FFE74D"
            // onOpenChange={setOpen}
        >
            {props.children}
        </Popover>
    );
};

export default ProductSelectButtonPopover;
