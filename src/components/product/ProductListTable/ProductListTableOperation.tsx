import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { FormatedProductItem, ProductType } from '@src/types/product';
import { Button, Space } from 'antd';
import ProductSelectButtonPopover from './ProductSelectButtonPopover';

export interface ProductListTableOperationProps {
    item: FormatedProductItem;
    fisrtPopoverTaskId?: number;
    onClickSelection: () => void;
}

const ProductListTableOperation = (props: ProductListTableOperationProps) => {
    const { product, taskId, ablity } = props.item;
    const isBDProduct = product.productType === ProductType.BD;

    return (
        <Space>
            <ProductSelectButtonPopover shouldPop={taskId === props.fisrtPopoverTaskId}>
                <Button
                    style={{ paddingLeft: 0, paddingRight: 0, width: 44 }}
                    disabled={!ablity.select}
                    type="link"
                    onClick={props.onClickSelection}
                >
                    {isBDProduct ? '/' : '圈商家'}
                </Button>
            </ProductSelectButtonPopover>

            <Button
                type="link"
                href={bellwetherLinkParse(`./detail?taskId=${taskId}`)}
                target="_blank"
                style={{ paddingLeft: 0, paddingRight: 0 }}
            >
                查看
            </Button>
        </Space>
    );
};

export default ProductListTableOperation;
