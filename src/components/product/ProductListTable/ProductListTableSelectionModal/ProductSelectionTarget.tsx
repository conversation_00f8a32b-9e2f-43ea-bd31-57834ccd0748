import { Flex, InputNumber, Typography } from 'antd';

const { Text } = Typography;

export interface ProductSelectionTargetProps {
    target: number;
    value?: number;
    onChange?: (v: number | null) => void;
}

const ProductSelectionTarget = (props: ProductSelectionTargetProps) => {
    return (
        <>
            <Flex align="center" style={{ marginBottom: 8 }}>
                <Text type="secondary">总部目标：</Text>
                <Text strong>{props.target}%</Text>
            </Flex>
            <Flex align="center">
                <Text type="secondary">城市目标：</Text>
                <InputNumber
                    style={{ width: 110 }}
                    value={props.value}
                    onChange={props.onChange}
                    placeholder="请输入"
                    min={props.target}
                    max={100}
                    precision={1}
                    step={0.1}
                    addonAfter={'%'}
                />
            </Flex>
        </>
    );
};

export default ProductSelectionTarget;
