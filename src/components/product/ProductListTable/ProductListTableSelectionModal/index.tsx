import { FormatedProductItem, ProductStatus } from '@src/types/product';
import { useRequest } from 'ahooks';
import { Form, Modal, Space, Typography, Divider, message, UploadFile, App } from 'antd';
import excelImg from '@src/assets/images/excel.png';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import S3Upload from '@src/components/S3Upload';
import ProductSelectionTarget from './ProductSelectionTarget';
import './style.scss';
import { APISpec, BFFResponse, apiCaller } from '@mfe/cc-api-caller-pc';
import { getPrefix } from '@src/module/request/getPrefix';
import { Region } from '@src/utils/getS3Host';

const { Text } = Typography;

type Signature = APISpec['/product/s3/signature']['response'];

export interface ProductListTableSelectionModalProps {
    item?: FormatedProductItem;
    open: boolean;
    onCancel: () => void;
    onConfirm: () => void;
}

const ProductListTableSelectionModal = (props: ProductListTableSelectionModalProps) => {
    const [form] = Form.useForm();
    const { modal } = App.useApp();

    const normFile = e => {
        if (Array.isArray(e)) {
            return e;
        }
        return e?.fileList;
    };
    const onConfirm = async (values: { customTargetPercent: number; circleGrantTargetExcel: UploadFile<string>[] }) => {
        if (!props.item) {
            return;
        }
        const { circleGrantTargetExcel, customTargetPercent } = values;

        if (circleGrantTargetExcel[0]?.status !== 'done') {
            message.error('请等待上传完成');
            return;
        }

        const url = circleGrantTargetExcel[0]?.response;

        const res = await apiCaller.send('/product/task/submitTaskDetail', {
            taskId: props.item.taskId,
            customTargetPercent,
            circleGrantTargetExcel: url,
        });

        if (res.code !== 0) {
            return;
        }

        props.onConfirm();
        // 没有修改圈选商家的话，那么就不必提示成功和失败的个数
        if (!url) {
            message.success('操作成功！');
            return;
        }

        const { circleSuccessTotal, circleFailTotal, circleFailExcel } = res.data;
        modal.info({
            title: '圈选成功',
            content: (
                <>
                    <Typography.Paragraph style={{ marginTop: 20 }}>
                        成功上传{circleSuccessTotal}个商家，失败{circleFailTotal}个商家
                    </Typography.Paragraph>
                    {circleFailExcel ? (
                        <Typography.Paragraph>
                            点击下载
                            <Typography.Link style={{ marginLeft: 4 }} href={circleFailExcel} target="_blank">
                                失败商家明细
                            </Typography.Link>
                        </Typography.Paragraph>
                    ) : null}
                </>
            ),
        });
    };

    const { run, loading } = useRequest(onConfirm, { manual: true });

    const downloadLink = async (params?) => {
        if (!props.item) {
            return null;
        }

        const res = await apiCaller.send('/product/grant/exportGrantProductList', {
            productId: props.item.product.productId,
            productType: props.item.product.productType,
            ...params,
        });

        if (res.code !== 0) {
            return;
        }

        message.success(res.msg);
    };

    if (!props.item) {
        return null;
    }

    const initialFileList =
        props.item.status === ProductStatus.已圈选
            ? [
                  {
                      uid: '-1',
                      name: '已圈选商家范围.xlsx',
                      status: 'done',
                  },
              ]
            : [];

    return (
        <Modal
            wrapClassName="product-selection-modal"
            title="圈选商家"
            open={props.open}
            onCancel={props.onCancel}
            onOk={form.submit}
            confirmLoading={loading}
        >
            <Form
                key={props.item.taskId}
                form={form}
                layout="vertical"
                initialValues={{
                    circleGrantTargetExcel: initialFileList,
                    customTargetPercent: props.item.customTargetPercent,
                }}
                onFinish={run}
                style={{ marginTop: 24 }}
            >
                <Form.Item
                    label={
                        <>
                            <Text strong className="product-selection-modal__step--title">
                                第一步
                            </Text>
                            <Text>下载商增建议的门店范围，到大象公众号查收</Text>
                        </>
                    }
                >
                    <Space
                        onClick={() => downloadLink()}
                        className="product-selection-modal__file"
                        split={<Divider type="vertical" />}
                    >
                        <div>
                            <img className="product-selection-modal__img" src={excelImg} />
                            <Text>建议门店范围.xlsx</Text>
                        </div>
                        <DownloadOutlined />
                    </Space>
                </Form.Item>

                <Form.Item
                    label={
                        <>
                            <Text strong className="product-selection-modal__step--title">
                                第二步
                            </Text>
                            <Text>筛选以上excle内容后，上传商家池</Text>
                        </>
                    }
                    name="circleGrantTargetExcel"
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                    rules={[
                        { required: true, message: '请上传商家池' },
                        {
                            validator: (_, value) => {
                                const uploadSuccess = value.every(
                                    it => it.response || it.status === 'uploading' || it.status === 'done',
                                );
                                if (uploadSuccess) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(value[0]?.error || '上传失败');
                            },
                        },
                    ]}
                >
                    <S3Upload
                        draggable
                        accept=".xlsx"
                        maxCount={1}
                        bucket="crm-product"
                        region={Region.BEI_JING_02}
                        sigUrl={getPrefix('/product/s3/signature') + '/product/s3/signature'}
                        itemRender={(originNode, file) => {
                            return (
                                <div
                                    className="product-selection-modal__filelist"
                                    onClick={e => {
                                        const target = e.target as HTMLElement;
                                        if (file.error || target.className !== 'ant-upload-list-item-name') {
                                            return;
                                        }
                                        e.preventDefault();
                                        downloadLink({ valid: 1 });
                                    }}
                                >
                                    {originNode}
                                </div>
                            );
                        }}
                        s3UploadProps={{
                            signAdaptor: (res: BFFResponse<Signature>) => {
                                if (res.code !== 0) {
                                    return res;
                                }
                                const random = `${Math.random()}`.split('.')[1]; // 取随机的小数部分
                                res.data.key = `用户圈选商家池-${random}-${new Date().getTime()}`;

                                return res;
                            },
                        }}
                        onDrop={e => {
                            const files = e.dataTransfer.files;
                            const notValid = Array.from(files).some(file => !file.name.endsWith('.xlsx'));
                            if (!notValid) {
                                return;
                            }

                            message.error('请上传xlsx文件');
                        }}
                    >
                        <Space className="product-selection-modal__drag">
                            <UploadOutlined />
                            <Text>选择或拖拽文件到此处</Text>
                        </Space>
                    </S3Upload>
                </Form.Item>
                <Form.Item
                    label={
                        <>
                            <Text strong className="product-selection-modal__step--title">
                                第三步
                            </Text>
                            <Text>设置城市目标</Text>
                            <Text type="secondary">（非必填）</Text>
                        </>
                    }
                    name="customTargetPercent"
                    required
                >
                    <ProductSelectionTarget target={props.item.product.targetPercent} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ProductListTableSelectionModal;
