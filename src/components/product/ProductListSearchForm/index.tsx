import { But<PERSON>, Di<PERSON>r, Flex, Form, FormInstance, Input, InputNumber, Radio, Select, Space } from 'antd';
import AdaptiveGrid from '../../AdaptiveGrid';
import OrganizationSelectorWithRole from '../../rooPlus/OrganizationSelectorWithRole';
import {
    productDispatchOptions,
    productQualifiedOptions,
    productStatusMap,
    productTagOptions,
    productTypeMap,
} from '@src/constants/product';
import PickerWithType from '../../PickerWithType';
import './style.scss';
import { ProductSelectionStatus, ProductStatus, ProductType } from '@src/types/product';
import { SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

export interface ProductListSearchFormProps {
    form: FormInstance;
    submit: () => void;
    submitting: boolean;
}

const withAll = <T,>(items: { label?: string; value: T }[] = []) => [
    {
        label: '全部',
        value: null,
    },
    ...items,
];

const productSelectionOptionsWithProductType: {
    label: string;
    value: { circleStatus: ProductSelectionStatus; productType: ProductType };
}[] = [
    {
        label: `${productTypeMap.get(ProductType.CM)}-${productStatusMap.get(ProductStatus.待圈选)}`,
        value: { circleStatus: ProductSelectionStatus.待圈选, productType: ProductType.CM },
    },
    {
        label: `${productTypeMap.get(ProductType.CM)}-${productStatusMap.get(ProductStatus.已圈选)}`,
        value: { circleStatus: ProductSelectionStatus.已圈选, productType: ProductType.CM },
    },
    {
        label: `${productTypeMap.get(ProductType.BD)}-${productStatusMap.get(ProductStatus.不需要圈选)}`,
        value: { circleStatus: ProductSelectionStatus.不需要圈选, productType: ProductType.BD },
    },
];

const ProductListSearchForm = (props: ProductListSearchFormProps) => {
    const onOrgInit = orgIdList => {
        props.form.setFieldsValue({ orgIdList });
        props.submit();
    };

    const onReset = () => {
        // TODO, 组织结构的重置
        props.form.resetFields();
    };

    return (
        <div className="product-list-search">
            <Form
                form={props.form}
                initialValues={{
                    dateRange: { type: 'month', value: dayjs() },
                    productTag: null,
                    selectionStatusWithProductType: null,
                    grantStatus: null,
                    qualified: null,
                }}
            >
                <AdaptiveGrid divide={4} gutter={32}>
                    <Form.Item name="orgIdList" label="组织结构">
                        <OrganizationSelectorWithRole multiple authCode="product" onInit={onOrgInit} />
                    </Form.Item>
                    <Form.Item name="taskId" label="任务ID">
                        <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入任务ID"
                            controls={false}
                            precision={0}
                        />
                    </Form.Item>
                    <Form.Item name="productKeyword" label="任务名称">
                        <Input placeholder="请输入任务名称" />
                    </Form.Item>
                    <Form.Item name="productTag" label="任务类型">
                        <Select placeholder="请选择任务类型" options={withAll(productTagOptions)} allowClear />
                    </Form.Item>
                    <Form.Item name="dateRange" label="创建时间">
                        <PickerWithType />
                    </Form.Item>
                </AdaptiveGrid>

                <Flex className="product-list-search__status" align="center">
                    <Form.Item name="selectionStatusWithProductType" label="圈选状态">
                        <Radio.Group>
                            {withAll(productSelectionOptionsWithProductType).map(it => (
                                <Radio value={it.value} key={it.label}>
                                    {it.label}
                                </Radio>
                            ))}
                        </Radio.Group>
                    </Form.Item>
                    <Divider type="vertical" style={{ marginLeft: 32, marginRight: 48 }} />
                    <Form.Item name="grantStatus" label="下发状态">
                        <Radio.Group>
                            {withAll(productDispatchOptions).map(it => (
                                <Radio value={it.value} key={it.value}>
                                    {it.label}
                                </Radio>
                            ))}
                        </Radio.Group>
                    </Form.Item>
                </Flex>

                <Flex className="product-list-search__status" justify="space-between">
                    <Form.Item name="qualified" label="达标状态">
                        <Radio.Group>
                            {withAll(productQualifiedOptions).map(it => (
                                <Radio value={it.value} key={it.value}>
                                    {it.label}
                                </Radio>
                            ))}
                        </Radio.Group>
                    </Form.Item>
                    <Space>
                        <Button onClick={onReset}>重置</Button>
                        <Button
                            type="primary"
                            loading={props.submitting}
                            onClick={props.submit}
                            icon={<SearchOutlined />}
                        >
                            确定
                        </Button>
                    </Space>
                </Flex>

                {/* <Form.Item noStyle shouldUpdate>
                    {() => (
                        <Typography>
                            <pre>{JSON.stringify(props.form.getFieldsValue(), null, 2)}</pre>
                        </Typography>
                    )}
                </Form.Item> */}
            </Form>
        </div>
    );
};

export default ProductListSearchForm;
