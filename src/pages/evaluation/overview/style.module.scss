.evaluation-overview {
    padding-bottom: 20px;
    padding-top: 20px;

    :global {
        .ant-form-item {
            margin-bottom: 0;
        }

        .ant-statistic {
            .ant-statistic-content {
                color: #222222;
                font-weight: 700;
                font-size: 36px;

                &-suffix {
                    font-weight: 400;
                    font-size: 16px;
                    vertical-align: middle;
                }
            }

            .ant-statistic-title {
                color: #222222;
                font-size: 14px;
            }
        }

        .evaluation-block + .evaluation-block {
            margin-top: 30px;
        }

        .evaluation-block {
            position: relative;

            &__icon {
                margin-left: 5px;
            }
            &__chart {
                border: 1px solid #e5e5e5;
                padding: 28px;
            }

            &__org-export {
                position: absolute;
                right: 0;
                z-index: 1;
                top: 10px;

                .ant-btn {
                    color: #ff6a00;
                }
            }

            &__stats {
                > * {
                    flex: 1;
                    flex-basis: 160px;
                }
                &__item {
                    border: 1px solid #e5e5e5;
                    padding: 24px;
                    height: 134px;

                    &__sub {
                        color: #555555;
                        font-weight: 500;
                        font-size: 12px;
                    }
                }
            }

            &__org__head {
                &--1:not(td) {
                    background-color: #e5e5e5 !important;
                }

                &--2:not(td) {
                    background-color: #f2f2f2 !important;
                }

                &--3:not(td) {
                    background-color: #f7f8fa !important;
                }
            }
        }

        .annotation {
            font-size: 10px;

            &__higher {
                color: red;

                &::before {
                    content: '↑';
                }
            }

            &__lower {
                color: green;

                &::before {
                    content: '↓';
                }
            }
        }
    }
}
