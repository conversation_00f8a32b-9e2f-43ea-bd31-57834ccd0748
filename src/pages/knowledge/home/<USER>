import React from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { APISpec } from '@mfe/cc-api-caller-pc';
import KnowledgeCard from './components/KnowledgeCard';
import { InfoCircleOutlined } from '@ant-design/icons';

type KnowledgeListType = APISpec['/manage/dataset/list']['response']['list'];

// 统计数据配置
const STATS_CONFIG = [
    {
        title: '知识总数',
        key: 'wikiCount',
        tip: '知识库去重后WIKI总数',
    },
    {
        title: '上周活跃知识',
        key: 'wikiRecallCount',
        tip: '上周助手回答调用的WIKI求和（每周更新）',
    },
    {
        title: '上周更新知识',
        key: 'wikiUpdateCount',
        tip: '上周有更新的WIKI求和（每周更新）',
    },
] as const;

const App: React.FC = () => {
    const { data: listData } = useRequest(async () => {
        const res = await apiCaller.send('/manage/dataset/list', {
            pageNum: 1,
            pageSize: 20,
        });
        if (res.code !== 0) {
            return {
                list: [],
                total: 0,
                pageNum: 1,
                pageSize: 20,
            };
        }
        return res.data;
    });

    const { data: stasData } = useRequest(async () => {
        const res = await apiCaller.get('/manage/dataset/card/statistics', {});
        if (res.code !== 0) {
            return {
                fragmentCount: 0,
                wikiRecallCount: 0,
                wikiUpdateCount: 0,
            };
        }
        return res.data;
    });

    const handleUpload = () => {
        location.href = './upload';
    };

    return (
        <div className="knowledge-home">
            {/* 上传按钮 */}
            <div
                style={{
                    textAlign: 'right',
                    marginBottom: 16,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}
            >
                <h2>知识库管理</h2>
                <Button type="primary" onClick={handleUpload}>
                    上传知识
                </Button>
            </div>
            {/* 统计数据 */}
            <div className="knowledge-home__stats">
                {STATS_CONFIG.map(stat => (
                    <div key={stat.title} className="stat-card">
                        <div className="stat-card__title">
                            <span>{stat.title}</span>
                            <Tooltip title={stat.tip}>
                                <InfoCircleOutlined />
                            </Tooltip>
                        </div>
                        <div className="stat-card__value">{stasData?.[stat.key] || '-'}</div>
                    </div>
                ))}
            </div>

            {/* 知识库列表 */}
            <div className="knowledge-home__grid">
                {(listData?.list || []).map(item => (
                    <KnowledgeCard
                        key={item.id}
                        title={item.name}
                        description={item.desc}
                        updateTime={item.updateTime}
                        sliceCount={item.fragmentSize}
                        onClick={() => (location.href = `./detail/${item.id}`)}
                    />
                ))}
            </div>
        </div>
    );
};

export default App;
