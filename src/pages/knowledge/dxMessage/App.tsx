import { apiCaller, APISpec, MethodType } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';
import React, { useState } from 'react';
import { Button, Input, Form, message, Card, Row, Col } from 'antd';

interface DxMsgParams {
    question: string;
    dxUid: string;
}

const App = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    // DX消息请求
    const { run: runSendDxMsg } = useRequest(
        async (params: DxMsgParams) => {
            setLoading(true);
            try {
                const res = await apiCaller.send('/dx/mock/pubMsg' as keyof APISpec, params, {
                    method: MethodType.FORM_DATA,
                    silent: true,
                });
            } finally {
                setLoading(false);
                message.success('发送成功, 请查看大象消息');
            }
        },
        {
            manual: true,
        },
    );

    const handlePushMsg = (values: DxMsgParams) => {
        runSendDxMsg(values);
    };

    return (
        <div className="dx-message-page">
            <Card style={{ marginBottom: 16 }} rootClassName="wiki-format-form-card">
                <Form form={form} onFinish={handlePushMsg}>
                    <Row gutter={24}>
                        <Col span={24}>
                            <Form.Item
                                name="question"
                                label="问题"
                                rules={[{ required: true, message: '这是必填项' }]}
                                style={{ flex: 1 }}
                            >
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={24}>
                        <Col span={24}>
                            <Form.Item
                                name="dxUid"
                                label="大象UID"
                                rules={[{ required: true, message: '这是必填项' }]}
                                style={{ flex: 1 }}
                            >
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item>
                        <Button loading={loading} type="primary" htmlType="submit">
                            提问
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </div>
    );
};

export default App;
