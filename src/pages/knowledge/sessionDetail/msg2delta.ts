import { text2delta } from '@src/pages/knowledge/components/editor/markRender';
import { ALL_KEYS } from '../message';

interface Data {
    conversationType: 'question' | 'answer';
    currentContent?: string;
    prefixTextContent?: string;
    postTextContent?: string;
    imageList?: string[];
    selectionItems?: {
        abilityType: number;
        subAbilityType?: number;
        content: string;
        url?: string;
        operationType: number;
    }[];
}
export const delta2text = (json: any[] | string | null) => {
    if (!json) {
        return json;
    }
    let res;
    if (Array.isArray(json)) {
        res = json;
    } else {
        try {
            res = JSON.parse(json);
        } catch (e) {
            return json;
        }
    }
    if (!Array.isArray(res)) {
        return json;
    }
    return res
        ?.map(item => {
            if (typeof item.insert === 'string') {
                return item.insert;
            }
            return '';
        })
        .join('');
};

const msg2delta = (data: Data) => {
    let res: any[] = [];
    if (data.prefixTextContent) {
        res = [...res, ...text2delta(data.prefixTextContent)];
    }
    if (data.currentContent) {
        res = [...res, ...text2delta(data.currentContent)];
    }
    if (data.selectionItems?.length) {
        res = [...res, { insert: { options: data.selectionItems } }];
    }
    if (data.postTextContent) {
        res = [...res, ...text2delta(data.postTextContent)];
    }
    if (data.imageList?.length) {
        res = [...res, { insert: '\n' }, ...data.imageList.map(item => ({ insert: { image: item } }))];
    }

    // 媒体数据前后添加换行
    const finalRes = res.map((v, i) => {
        if (typeof v.insert === 'string') {
            if ((res[i + 1]?.insert.image || res[i + 1]?.insert.video) && !v.insert.endsWith('\n')) {
                v.insert = v.insert + '\n';
            }
            if (res[i - 1]?.insert.image || (res[i - 1]?.insert.video && !v.insert.startsWith('\n'))) {
                v.insert = '\n' + v.insert;
            }
        }
        if (v.insert.markdown) {
            v.insert = v.insert.markdown.text;
        }
        if (typeof v.insert === 'object' && Object.keys(v.insert).some(key => !ALL_KEYS.includes(key))) {
            v.insert = '\n【暂不支持该格式消息】\n';
        }
        return v;
    });

    return finalRes.length > 0 ? finalRes : [{ insert: '【用户主动终止或系统发生错误】' }];
};
export default msg2delta;
