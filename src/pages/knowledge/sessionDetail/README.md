# 会话详情页面 Mock 数据使用说明

## 功能概述

本页面支持在开发环境下使用 Mock 数据进行测试，方便开发和调试 TT 工单相关功能。

## Mock 数据类型

### 1. 单个 TT 工单 (`single`)

-   会话 ID: 822723
-   用户: 应金伟 (yingjinwei)
-   状态: 已转 TT 工单
-   TT 工单数量: 1 个
-   功能: 点击"查看 TT 工单"按钮直接打开链接

### 2. 多个 TT 工单 (`multiple`)

-   会话 ID: 822724
-   用户: 张三 (zhangsan)
-   状态: 已转 TT 工单
-   TT 工单数量: 3 个
-   功能: 点击"查看 TT 工单"按钮显示下拉菜单选择

### 3. 无 TT 工单 (`noTT`)

-   会话 ID: 822725
-   用户: 李四 (lisi)
-   状态: 未转 TT 工单
-   TT 工单数量: 0 个
-   功能: 不显示"查看 TT 工单"按钮

## 使用方法

### 启用 Mock 数据

1. 确保在开发环境 (`NODE_ENV === 'development'`)
2. 页面顶部会显示 Mock 数据控制面板
3. 点击"启用 Mock"按钮开启 Mock 模式

### 切换 Mock 数据类型

启用 Mock 后，可以通过以下按钮切换不同的测试场景：

-   **单个 TT 工单**: 测试单个链接的直接跳转功能
-   **多个 TT 工单**: 测试多个链接的下拉菜单功能
-   **无 TT 工单**: 测试无 TT 工单时的 UI 显示

### 关闭 Mock 数据

点击"关闭 Mock"按钮，页面将恢复使用真实 API 数据。

## 技术实现

-   Mock 数据存储在 `mock.ts` 文件中
-   使用 `localStorage` 保存 Mock 配置
-   通过环境变量判断是否显示 Mock 控制面板
-   支持动态切换不同的 Mock 数据场景

## 测试场景

1. **UI 测试**: 验证不同状态下的界面显示
2. **交互测试**: 测试按钮点击和下拉菜单功能
3. **链接测试**: 验证 TT 工单链接的正确跳转
4. **边界测试**: 测试无数据、单个数据、多个数据的处理

## 注意事项

-   Mock 功能仅在开发环境下可用
-   生产环境不会显示 Mock 控制面板
-   切换 Mock 类型会自动刷新页面
-   Mock 数据包含完整的会话信息和对话记录
