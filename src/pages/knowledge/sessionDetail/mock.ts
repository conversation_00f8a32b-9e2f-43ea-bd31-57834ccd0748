import { SessionDetailResponse } from './types';

// 单个TT工单链接的mock数据
export const mockSessionDetailSingle: SessionDetailResponse = {
    id: '822723',
    mis: 'yingjinwei',
    name: '应金伟',
    createTime: '2025-06-03 19:40:00',
    orgInfos: ['外卖部门-华东大区-浙南区域-温州中分-金平团队-金华三组'],
    domainList: ['票券收银'],
    phraseNameList: ['人工服务'],
    submittedTt: true,
    remark: '你是体验官(公司股东)？请再详细描述下题。比如"票券收银问了，需要实际操作"。',
    ttLinkList: [
        {
            id: 12345,
            link: 'https://tt.sankuai.com/ticket/detail?id=12345',
        },
    ],
};

// 多个TT工单链接的mock数据
export const mockSessionDetailMultiple: SessionDetailResponse = {
    id: '822724',
    mis: 'zhang<PERSON>',
    name: '张三',
    createTime: '2025-06-03 20:15:00',
    orgInfos: ['外卖部门-华南大区-广东区域-深圳中分-南山团队-科技园组'],
    domainList: ['订单管理', '支付系统'],
    phraseNameList: ['订单异常处理', '支付问题咨询'],
    submittedTt: true,
    remark: '用户反馈订单支付异常，已转多个相关TT工单处理',
    ttLinkList: [
        {
            id: 67890,
            link: 'https://tt.sankuai.com/ticket/detail?id=67890',
        },
        {
            id: 67891,
            link: 'https://tt.sankuai.com/ticket/detail?id=67891',
        },
        {
            id: 67892,
            link: 'https://tt.sankuai.com/ticket/detail?id=67892',
        },
    ],
};

// 无TT工单链接的mock数据
export const mockSessionDetailNoTT: SessionDetailResponse = {
    id: '822725',
    mis: 'lisi',
    name: '李四',
    createTime: '2025-06-03 21:30:00',
    orgInfos: ['外卖部门-华北大区-北京区域-朝阳中分-CBD团队-国贸组'],
    domainList: ['商家管理'],
    phraseNameList: ['商家入驻咨询'],
    submittedTt: false,
    remark: '',
    ttLinkList: [],
};

// 对话记录mock数据
export const mockConversations = {
    conversations: [
        {
            id: 1,
            type: 'user',
            content: '你好，我想咨询一下关于票券收银的问题',
            timestamp: '2025-06-03 19:35:00',
        },
        {
            id: 2,
            type: 'assistant',
            content: '您好！我是智能助手，很高兴为您服务。请问您遇到了什么具体的票券收银问题呢？',
            timestamp: '2025-06-03 19:35:15',
        },
        {
            id: 3,
            type: 'user',
            content: '我在使用票券收银功能时，发现无法正常核销优惠券，系统提示错误',
            timestamp: '2025-06-03 19:36:00',
        },
        {
            id: 4,
            type: 'assistant',
            content:
                '感谢您的反馈。票券核销异常可能有以下几种原因：\n1. 优惠券已过期\n2. 优惠券使用条件不满足\n3. 系统网络异常\n\n请问您能提供具体的错误提示信息吗？',
            timestamp: '2025-06-03 19:36:30',
        },
        {
            id: 5,
            type: 'user',
            content: '错误提示是"券码无效"，但是这个券是刚刚发放的，应该没有问题',
            timestamp: '2025-06-03 19:37:00',
        },
        {
            id: 6,
            type: 'assistant',
            content:
                '根据您描述的情况，"券码无效"错误通常是由于：\n1. 券码格式不正确\n2. 券码已被使用\n3. 券码生成异常\n\n建议您联系技术支持进行进一步排查，我已为您转接人工服务。',
            timestamp: '2025-06-03 19:37:45',
        },
    ],
    total: 6,
};

// 根据环境变量或配置选择使用哪个mock数据
export const getCurrentMockData = () => {
    const mockType = localStorage.getItem('mockType') || 'single';

    switch (mockType) {
        case 'multiple':
            return mockSessionDetailMultiple;
        case 'noTT':
            return mockSessionDetailNoTT;
        default:
            return mockSessionDetailSingle;
    }
};

// 设置mock类型的工具函数
export const setMockType = (type: 'single' | 'multiple' | 'noTT') => {
    localStorage.setItem('mockType', type);
    window.location.reload();
};
