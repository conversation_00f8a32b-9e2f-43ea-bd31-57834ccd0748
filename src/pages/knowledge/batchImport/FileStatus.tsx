import { CheckCircleFilled, CloseCircleFilled, DeleteOutlined, LinkOutlined, LoadingOutlined } from '@ant-design/icons';
import { useHover } from 'ahooks';
import { Button, Space, Typography } from 'antd';
import { ReactElement, useRef } from 'react';

const FileStatus = ({ file = {}, setFileList }: { file: any; setFileList: (fileList: any[]) => void }) => {
    const fileRef = useRef<HTMLDivElement>(null);
    const isHoverFile = useHover(fileRef);
    let statusEle: ReactElement | null = null;
    switch (file.status) {
        case 'done':
            statusEle = (
                <Typography.Text>
                    <CheckCircleFilled style={{ color: 'yellowgreen' }} />
                    上传成功
                </Typography.Text>
            );
            break;
        case 'uploading':
            statusEle = (
                <Typography.Text>
                    <LoadingOutlined spin />
                    上传{file.percent || 0}%
                </Typography.Text>
            );
            break;
        case 'error':
            statusEle = (
                <Typography.Text>
                    <CloseCircleFilled style={{ color: 'red' }} />
                    上传失败，请重新上传
                </Typography.Text>
            );
    }
    return (
        <Space
            size={'large'}
            ref={fileRef}
            style={{
                ...(isHoverFile ? { background: '#f2f2f2', borderRadius: 10 } : {}),
                ...(!file.name ? { opacity: 0 } : {}),
            }}
            key={'file'}
        >
            <Typography.Text>
                <LinkOutlined />
                {file.name}
            </Typography.Text>
            <div style={{ width: 200 }}>
                {!isHoverFile ? (
                    statusEle
                ) : (
                    <Button
                        type={'link'}
                        icon={<DeleteOutlined />}
                        onClick={e => {
                            e.stopPropagation();
                            setFileList([]);
                        }}
                    />
                )}
            </div>
        </Space>
    );
};
export default FileStatus;
