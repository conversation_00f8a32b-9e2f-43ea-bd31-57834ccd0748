import { PlusCircleFilled } from '@ant-design/icons';
import { Upload, App as AntdApp, Space, Typography, Button, UploadFile } from 'antd';
import axios from 'axios';
import FileStatus from './FileStatus';
import { useState } from 'react';

const Uploader = ({ setProcessId, processId }) => {
    const { message, modal } = AntdApp.useApp();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const getFileFromFileList = fl => fl.filter(v => !!v.status)[fl.length - 1];

    return (
        <div className="custom-hover">
            <Upload.Dragger
                customRequest={async ({ file, onError, onProgress, onSuccess }) => {
                    const formData = new FormData();
                    formData.append('file', file);
                    const res = (
                        await axios.post(
                            `${import.meta.env.VITE_API_PREFIX}/assistant/manage/phrase/standard/batch/upload`,
                            formData,
                        )
                    ).data;
                    onProgress?.({ percent: 100 });
                    if (res.code !== 0) {
                        message.error(res.msg);
                        onError?.({ message: res.msg, name: res.msg }, res);
                    } else {
                        setProcessId(res.data.processId);
                        onSuccess?.(formData, res);
                    }
                    return res;
                }}
                action={`${import.meta.env.VITE_API_PREFIX}/assistant/manage/phrase/standard/batch/upload`}
                accept={'.xls,.xlsx'}
                onChange={v => {
                    setFileList(v.fileList);
                }}
                beforeUpload={f => {
                    if (processId) {
                        return new Promise((res, rej) => {
                            modal.confirm({
                                title: '数据重置警告',
                                content: '如果您要继续上传文件的话，下方数据会被重置，是否要重置？',
                                onOk: () => res(true),
                                onCancel: () => rej(false),
                                icon: null,
                            });
                        });
                    }
                    if (f.size > 2 * 1024 * 1024) {
                        message.error('文件大小不能超过2MB');
                        return false;
                    }
                }}
                showUploadList={false}
            >
                <Space direction={'vertical'}>
                    <Space>
                        <PlusCircleFilled style={{ color: 'orange', fontSize: '1.2rem' }} />
                        <Typography.Text>
                            拖拽文件到这里，或
                            <Button type={'link'} style={{ padding: 0 }}>
                                点此批量上传
                            </Button>
                        </Typography.Text>
                    </Space>
                    <Typography.Text type="secondary">仅支持上传xls, xlsx格式的文件</Typography.Text>
                    {/*Upload文件列表展示在上传按钮外，为贴合需求，手动实现上传文件列表*/}
                    <FileStatus file={getFileFromFileList(fileList)} setFileList={setFileList} />
                </Space>
            </Upload.Dragger>
        </div>
    );
};
export default Uploader;
