import React, { useState } from 'react';
import { Form, Select, Input, Switch, Checkbox, Button, Table, message, Tag, Tooltip, Typography, Alert } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import { useRequest, useDebounceFn } from 'ahooks';
import type { UploadFormData, WikiItem, WikiChildResponse } from '../types';
import { UploadWikiEnum } from '../types';
import { getWikiIdFromUrl } from '../utils/getWikiIdFromUrl';
import { fetchBizLineList, fetchSubWikiList, fetchWikiDetail, startUpload } from '../services/wikiService';
import ConfigConflictModal from './ConfigConflictModal';
import { isConfigIdentical } from '../utils/checkConfigIdentical';
import { ColumnType } from 'antd/es/table';
import validateWikiUrl from '../utils/validateWikiUrl';
import getWikiType from '../utils/getWikiType';

interface UploadFormProps {
    uploadList: WikiItem[];
    setUploadList: React.Dispatch<React.SetStateAction<WikiItem[]>>;
    onNext: (batchId: string, datasetId: number) => void;
}

const YesTextTag = () => <Tag color="#ffcc33">是</Tag>;
const NoTextTag = () => (
    <Tag color="#f5f6fa" style={{ color: '#222' }}>
        否
    </Tag>
);

export { YesTextTag, NoTextTag };

interface SelectedConfigs {
    [key: string]: WikiItem;
}

const UploadForm: React.FC<UploadFormProps> = ({ uploadList, setUploadList, onNext }) => {
    const [form] = Form.useForm<UploadFormData>();
    const [messageApi, contextHolder] = message.useMessage();
    const [subWikiListLoading, setSubWikiListLoading] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [subWikiList, setSubWikiList] = useState<WikiChildResponse>([]);
    const [loading, setLoading] = useState(false);
    const [addDocumentLoading, setAddDocumentLoading] = useState(false);
    const [conflictModalVisible, setConflictModalVisible] = useState(false);
    const [conflictItems, setConflictItems] = useState<WikiItem[]>([]);
    const [pendingNewItems, setPendingNewItems] = useState<WikiItem[]>([]);
    const [documentTitle, setDocumentTitle] = useState<string>('');

    // 使用 useRequest 获取业务线列表
    const { data: bizLineList = [] } = useRequest(fetchBizLineList, {
        onSuccess: () => {
            const searchParams = new URLSearchParams(window.location.search);
            const datasetId = searchParams.get('datasetId');

            if (datasetId) {
                form.setFieldValue('bizLine', Number(datasetId));
            }
        },
        onError: () => {
            message.error('获取知识库列表失败');
        },
    });

    // 重置表单和状态
    const resetForm = ({ needResetForm = true }: { needResetForm?: boolean } = {}) => {
        setDocumentTitle('');
        setSubWikiList([]);
        setSelectedRowKeys([]);
        if (needResetForm) {
            form.resetFields();
        }
    };

    // 添加项目到列表
    const addItemsToList = (newItems: WikiItem[]) => {
        const newList = [...uploadList];
        newItems.forEach(item => {
            const existingIndex = newList.findIndex(
                existing => String(existing.wikiId) === String(item.wikiId) && existing.datasetId === item.datasetId,
            );

            if (existingIndex === -1) {
                // 如果不存在，直接添加
                newList.push(item);
            } else if (!isConfigIdentical(newList[existingIndex], item)) {
                // 如果存在但配置不一致，添加新的
                newList.push(item);
            }
            // 如果存在且配置一致，则跳过（自动去重）
        });

        setUploadList(newList);
        message.success('添加成功');
        resetForm();
    };

    const { run: handleUrlChange } = useDebounceFn(
        async (url: string) => {
            if (!url) {
                resetForm({ needResetForm: false });
                return;
            }
            if (!validateWikiUrl(url)) {
                message.error('请输入正确的学城连接');
                resetForm({ needResetForm: false });
                return;
            }
            const wikiId = getWikiIdFromUrl(url);
            if (!wikiId) {
                setDocumentTitle('');
                return;
            }

            try {
                setSubWikiListLoading(true);
                const wikiType = getWikiType(url);
                // 空间文档不请求 detail
                const promiseList = [
                    fetchSubWikiList(url),
                    wikiType === 'space' ? Promise.resolve() : fetchWikiDetail(wikiId),
                ];
                Promise.all(promiseList).then(([subWikiList, title]) => {
                    setSubWikiList((subWikiList || []) as any);
                    setSelectedRowKeys(((subWikiList || []) as any).map(item => item.wikiId)); //默认全部选中
                    setSubWikiListLoading(false);
                    setDocumentTitle((title || '') as string);
                });
            } catch (error) {
                setDocumentTitle('');
                setSubWikiListLoading(false);
                message.error('获取文档信息失败');
            }
        },
        { wait: 800 },
    );

    const handleInputUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const url = e.target.value;
        form.setFieldValue('url', url);
        handleUrlChange(url);
    };

    const handleAddDocument = async () => {
        try {
            setAddDocumentLoading(true);
            const values = await form.validateFields();
            const wikiId = getWikiIdFromUrl(values.url);

            if (!wikiId) {
                message.error('文档链接无效');
                return;
            }

            const wikiType = getWikiType(values.url);
            const wikiTitle = wikiType === 'nomarl' ? await fetchWikiDetail(wikiId) : 'space';

            if (!wikiTitle) {
                return;
            }

            const newItem: WikiItem = {
                wikiId: wikiId,
                title: wikiTitle,
                autoUpdate: values.autoUpdate ?? true,
                // newItem 的子项需要哪些是在下面手动选的，这里不需要在指定了
                needSubWiki: false,
                syncWikiAuth: values.syncWikiAuth ?? false,
                needReferWiki: values.needReferWiki ?? false,
                type: UploadWikiEnum.wiki,
                datasetId: Number(values.bizLine),
            };

            let subWikiListData: WikiItem[] = [];
            if (wikiType === 'space' && (!values?.needSubWiki || !subWikiList.length || !selectedRowKeys.length)) {
                message.error('上传空间时，至少选择一项子文档');
                return;
            }
            if (values?.needSubWiki) {
                subWikiListData = subWikiList
                    .filter(item => selectedRowKeys.includes(item.wikiId))
                    .map(item => ({
                        wikiId: item.wikiId,
                        title: item.title,
                        autoUpdate: values.autoUpdate ?? true,
                        needSubWiki: values.needSubWiki ?? false,
                        needReferWiki: values.needReferWiki ?? false,
                        syncWikiAuth: values.syncWikiAuth ?? false,
                        type: UploadWikiEnum.subWiki,
                        datasetId: Number(values.bizLine),
                    }));
            }

            // 合并所有新增的文档
            // 不添加空间文档
            const newItems = [wikiType === 'space' ? undefined : newItem, ...subWikiListData].filter(
                Boolean,
            ) as WikiItem[];
            const newList = [...uploadList].filter(Boolean);
            const conflictingItems: WikiItem[] = [];

            // 检查每个新项是否存在配置冲突
            newItems.forEach(item => {
                const existingItems = newList.filter(
                    existing =>
                        String(existing.wikiId) === String(item.wikiId) && existing.datasetId === item.datasetId,
                );

                if (existingItems.length > 0 && !existingItems.some(existing => isConfigIdentical(existing, item))) {
                    conflictingItems.push(item, ...existingItems);
                }
            });

            if (conflictingItems.length > 0) {
                setConflictItems(conflictingItems);
                setPendingNewItems(newItems);
                setConflictModalVisible(true);
                return;
            }
            addItemsToList(newItems);
        } catch (error) {
            message.error('添加文档失败');
        } finally {
            setAddDocumentLoading(false);
        }
    };

    // 处理配置冲突解决
    const handleConfigConflictResolve = (selectedConfigs: SelectedConfigs) => {
        const newList = [...uploadList];

        // 移除所有冲突项
        const itemsToRemove = conflictItems.map(item => ({
            wikiId: item.wikiId,
            datasetId: item.datasetId,
        }));

        const filteredList = newList.filter(
            item =>
                !itemsToRemove.some(
                    removeItem =>
                        String(removeItem.wikiId) === String(item.wikiId) && removeItem.datasetId === item.datasetId,
                ),
        );

        // 添加所有选中的配置
        Object.values(selectedConfigs).forEach(selectedConfig => {
            filteredList.push(selectedConfig);
        });

        // 使用对应文档的选中配置更新待添加项
        const updatedPendingItems = pendingNewItems.map(item => {
            const key = `${item.wikiId}-${item.datasetId}`;
            const selectedConfig = selectedConfigs[key];
            if (selectedConfig) {
                return {
                    ...item,
                    autoUpdate: selectedConfig.autoUpdate,
                    needSubWiki: selectedConfig.needSubWiki,
                    needReferWiki: selectedConfig.needReferWiki,
                };
            }
            return item;
        });

        // 添加更新后的待添加项
        updatedPendingItems.forEach(item => {
            if (
                !filteredList.some(
                    existing =>
                        String(existing.wikiId) === String(item.wikiId) && existing.datasetId === item.datasetId,
                )
            ) {
                filteredList.push(item);
            }
        });

        setUploadList(filteredList);
        message.success('配置已更新');
        setConflictModalVisible(false);
        setConflictItems([]);
        setPendingNewItems([]);
        resetForm();
    };

    const handleStartUpload = async () => {
        if (uploadList.length === 0) {
            message.warning('请先添加文档');
            return;
        }
        setLoading(true);
        try {
            const batchId = await startUpload(uploadList);
            if (batchId) {
                // 获取第一个文档的知识库ID
                const firstItem = uploadList[0];
                onNext(batchId, firstItem.datasetId);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = (index: number) => {
        const newList = [...uploadList];
        newList.splice(index, 1);
        setUploadList(newList);
    };

    // 表格列配置
    const columns: ColumnType<WikiItem>[] = [
        {
            title: '知识库',
            dataIndex: 'datasetId',
            key: 'datasetId',
            align: 'center',
            render: (id: number) => (bizLineList || []).find(item => item.datasetId === id)?.name,
        },
        {
            title: '文件名称',
            dataIndex: 'title',
            key: 'title',
            align: 'center',
        },
        {
            title: '是否自动更新',
            dataIndex: 'autoUpdate',
            key: 'autoUpdate',
            align: 'center',
            render: (value: boolean) => (value ? <YesTextTag /> : <NoTextTag />),
        },
        {
            title: '是否上传引用文档',
            dataIndex: 'needReferWiki',
            key: 'needReferWiki',
            align: 'center',
            render: (value: boolean) => (value ? <YesTextTag /> : <NoTextTag />),
        },
        {
            title: '操作',
            key: 'action',
            align: 'center',
            render: (_: any, __: any, index: number) => (
                <Button type="link" onClick={() => handleDelete(index)}>
                    删除
                </Button>
            ),
        },
    ];

    return (
        <>
            {contextHolder}
            <div className="knowledge-upload__form" style={{ paddingTop: 10 }}>
                <Alert
                    message='需授予"it_bdaiassistant"对上传文档的管理权限，否则文档无法解析；仅支持上传密级为C2、C3的学城文档，不支持上传C4密级的文档。'
                    type="warning"
                    showIcon
                />
                <Form
                    form={form}
                    layout="vertical"
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        backgroundColor: '#f5f6fa',
                        borderRadius: 8,
                        paddingTop: 20,
                    }}
                >
                    <div className="knowledge-upload__form-container">
                        <Form.Item
                            label="选择知识库"
                            name="bizLine"
                            rules={[{ required: true, message: '请选择知识库' }]}
                        >
                            <Select
                                placeholder="请选择知识库"
                                options={bizLineList}
                                fieldNames={{
                                    label: 'name',
                                    value: 'datasetId',
                                }}
                            />
                        </Form.Item>
                        <Form.Item
                            label="文档链接"
                            name="url"
                            style={{ width: '100%' }}
                            rules={[{ required: true, message: '请输入文档链接' }]}
                        >
                            <>
                                <Input placeholder="请输入文档链接" onChange={handleInputUrlChange} />
                                {documentTitle && (
                                    <Typography.Text type="secondary" style={{ marginTop: 4, display: 'block' }}>
                                        文档名称：{documentTitle}
                                    </Typography.Text>
                                )}
                            </>
                        </Form.Item>
                    </div>

                    <div className="knowledge-upload__form-container" style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ width: '300px', display: 'flex', alignItems: 'center' }}>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <p style={{ marginRight: 5, whiteSpace: 'nowrap' }}>
                                    自动更新
                                    <Tooltip title="自动同步文档更新，预计更新时间会有30分钟的延迟">
                                        <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                    </Tooltip>
                                </p>
                                <Form.Item name="autoUpdate" initialValue={true} valuePropName="checked">
                                    <Switch checkedChildren="开" unCheckedChildren="关" />
                                </Form.Item>
                            </div>
                        </div>
                        <div style={{ width: '300px' }}>
                            <Form.Item name="needSubWiki" initialValue={true} valuePropName="checked" label={null}>
                                <Checkbox
                                    onChange={e => {
                                        if (!e.target.checked) {
                                            setSelectedRowKeys([]);
                                        }
                                    }}
                                >
                                    上传子文档
                                    <Tooltip title="默认自动上传子文档下的所有次级文档。如无需上传某篇次级文档，请取消it_bdassistant的次级子文档管理权限">
                                        <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                    </Tooltip>
                                </Checkbox>
                            </Form.Item>
                        </div>
                        <div>
                            <Form.Item name="needReferWiki" initialValue={false} valuePropName="checked">
                                <Checkbox>
                                    上传引用文档
                                    <Tooltip title="仅针对上传文档中的引用文档进行解析">
                                        <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                    </Tooltip>
                                </Checkbox>
                            </Form.Item>
                        </div>
                    </div>
                    <div className="knowledge-upload__form-container" style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ width: '300px', display: 'flex', alignItems: 'center' }}>
                            <p style={{ marginRight: 10 }}>
                                知识权限
                                <Tooltip title="默认同步知识库权限，如涉及保密项目/试点项目/差异化知识，可以选择同步学城权限。多个权限之间取交集">
                                    <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                </Tooltip>
                            </p>
                            <Form.Item>
                                <Checkbox defaultChecked={true} disabled>
                                    同步知识库权限
                                </Checkbox>
                            </Form.Item>
                        </div>
                        <Form.Item name="syncWikiAuth" initialValue={false} valuePropName="checked">
                            <Checkbox>同步学城浏览权限</Checkbox>
                        </Form.Item>
                    </div>
                    <Form.Item dependencies={['needSubWiki']} noStyle>
                        {({ getFieldValue }) => {
                            const needSubWiki = getFieldValue('needSubWiki');
                            return needSubWiki ? (
                                <>
                                    <h3>子文档列表</h3>
                                    <Table
                                        dataSource={subWikiList}
                                        columns={[
                                            {
                                                title: (
                                                    <div>
                                                        一键全选
                                                        <Tooltip title="默认自动上传子文档下的所有次级文档。如无需上传某篇次级文档，请取消it_bdassistant的次级子文档管理权限">
                                                            <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                                        </Tooltip>
                                                    </div>
                                                ),
                                                dataIndex: 'title',
                                            },
                                        ]}
                                        locale={{
                                            emptyText: '暂无数据',
                                        }}
                                        loading={subWikiListLoading}
                                        rowKey={'wikiId'}
                                        pagination={false}
                                        className="sub-wiki-table"
                                        rowSelection={{
                                            type: 'checkbox',
                                            selectedRowKeys,
                                            onChange: (selectedRowKeys: React.Key[]) => {
                                                setSelectedRowKeys(selectedRowKeys);
                                            },
                                        }}
                                    />
                                </>
                            ) : null;
                        }}
                    </Form.Item>
                </Form>
                <Button
                    type="primary"
                    onClick={handleAddDocument}
                    loading={addDocumentLoading}
                    style={{ marginTop: 20 }}
                >
                    添加文档
                </Button>
                <div className="knowledge-upload__table">
                    <h2>待上传列表</h2>
                    <Table
                        columns={columns}
                        dataSource={uploadList}
                        rowKey={(record, index) => String(record.wikiId) + (index || 0)}
                        pagination={false}
                    />
                    <div style={{ marginTop: 16, textAlign: 'right' }}>
                        <Button
                            type="primary"
                            onClick={handleStartUpload}
                            loading={loading}
                            disabled={uploadList.length === 0}
                        >
                            开始上传
                        </Button>
                    </div>
                </div>
            </div>
            <ConfigConflictModal
                open={conflictModalVisible}
                onCancel={() => {
                    setConflictModalVisible(false);
                    setConflictItems([]);
                    setPendingNewItems([]);
                    setAddDocumentLoading(false);
                }}
                onOk={handleConfigConflictResolve}
                conflictItems={conflictItems}
            />
        </>
    );
};

export default UploadForm;
