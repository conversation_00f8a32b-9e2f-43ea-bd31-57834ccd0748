.knowledge-detail {
    padding: 24px;

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        &-title {
            font-size: 20px;
            font-weight: 500;
        }

        &-actions {
            display: flex;
            gap: 12px;
        }
    }

    &__search {
        margin-bottom: 24px;

        &-form {
            display: flex;
            gap: 16px;
            align-items: flex-start;

            .ant-form-item {
                margin-bottom: 0;
            }
        }
    }

    &__table {
        .ant-table-wrapper {
            background: #fff;
            border-radius: 8px;
        }
    }
}

.modify-info-modal {
    .ant-modal-body {
        padding-top: 12px;
    }
}

.archive-tree-select-wrapper {
    .ant-select-tree-treenode-disabled {
        .ant-select-tree-checkbox-disabled {
            display: none !important;
        }

        .ant-select-tree-node-content-wrapper {
            cursor: default !important;
        }

        .ant-select-tree-node-content-wrapper  {
            padding-left: 0 !important;
            color: #000 !important;

        }
    }
}