import React, { useState, useEffect } from 'react';
import { Drawer, Card, Tag, Empty, Button, Pagination, Spin } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import ArchiveTreeSelect from './ArchiveTreeSelect';
import type { FragmentResponse } from '../types';
import { LinkOutlined } from '@ant-design/icons';

interface FragmentDrawerProps {
    open: boolean;
    onClose: () => void;
    wikiId?: number;
    datasetId: number;
}

type Fragment = FragmentResponse['datasetFragmentInfoList'][0];

const FragmentDrawer: React.FC<FragmentDrawerProps> = ({ open, onClose, wikiId, datasetId }) => {
    const [selectedCategories, setSelectedCategories] = useState<string[] | undefined>(undefined);
    const [currentPage, setCurrentPage] = useState(1);

    const {
        data: fragmentData,
        run: fetchFragment,
        loading,
    } = useRequest(
        async () => {
            if (!wikiId)
                return {
                    datasetFragmentInfoList: [],
                    total: 0,
                };

            const params = {
                datasetId: datasetId,
                categories: selectedCategories?.length ? selectedCategories : undefined,
                wikiId,
                pageNum: currentPage,
                pageSize: 20,
            };

            const res = await apiCaller.send('/manage/dataset/wiki/fragment', params);

            if (res.code !== 0) return { datasetFragmentInfoList: [], total: 0 };
            return res.data;
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (open && wikiId) {
            fetchFragment();
        }
    }, [open, wikiId]);

    useEffect(() => {
        if (open && wikiId) {
            fetchFragment();
        }
    }, [currentPage, selectedCategories]);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    return (
        <Drawer title="文档切片" open={open} onClose={onClose} width={1000} className="fragment-drawer">
            <div className="fragment-drawer__filter">
                <ArchiveTreeSelect
                    datasetId={datasetId}
                    style={{ width: '80%', marginRight: 50 }}
                    multiple={true}
                    treeCheckable={true}
                    levelCanCheck={[3]}
                    onChange={(node: any) => {
                        const newSelectCategories = (node || []).map(item => item.namePath.join('-'));
                        setSelectedCategories(newSelectCategories);
                        setCurrentPage(1); // 重置页码
                    }}
                    placeholder="选择目录进行筛选"
                    onKeyDown={(e: React.KeyboardEvent) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            setCurrentPage(1); // 重置页码
                            fetchFragment();
                        }
                    }}
                />
                <Button
                    type="primary"
                    onClick={() => {
                        setCurrentPage(1); // 重置页码
                        fetchFragment();
                    }}
                >
                    搜索
                </Button>
            </div>

            <Spin spinning={loading}>
                <div className="fragment-drawer__content">
                    {fragmentData?.datasetFragmentInfoList?.length ? (
                        <>
                            <div className="fragment-drawer__list">
                                {fragmentData.datasetFragmentInfoList.map((fragment: Fragment, index: number) => (
                                    <Card key={index} className="fragment-drawer__item">
                                        <div className="fragment-drawer__item-header">
                                            <div className="fragment-drawer__item-title">
                                                <LinkOutlined style={{ marginRight: 2 }} />
                                                {fragment.name}
                                            </div>
                                            <div className="fragment-drawer__item-tags">
                                                {fragment.tags.map((tag: string) => (
                                                    <Tag
                                                        bordered={false}
                                                        color="orange"
                                                        style={{ margin: 0 }}
                                                        key={tag}
                                                    >
                                                        {tag}
                                                    </Tag>
                                                ))}
                                            </div>
                                        </div>
                                        <div className="fragment-drawer__item-content">{fragment.content}</div>
                                    </Card>
                                ))}
                            </div>
                            <div className="fragment-drawer__pagination">
                                <span>共{fragmentData?.total || 0}条</span>
                                <Pagination
                                    current={currentPage}
                                    pageSize={20}
                                    total={fragmentData?.total || 0}
                                    onChange={handlePageChange}
                                />
                            </div>
                        </>
                    ) : (
                        <Empty description="暂无切片数据" />
                    )}
                </div>
            </Spin>
        </Drawer>
    );
};

export default FragmentDrawer;
