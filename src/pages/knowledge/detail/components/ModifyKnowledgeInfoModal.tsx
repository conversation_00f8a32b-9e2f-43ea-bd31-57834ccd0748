import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { ModifyKnowledgeInfoParams, BizLineItem, MisSearchResponse, OrgSearchResponse } from '../types';
import AutoSearch from './AutoSearch';
interface ModifyKnowledgeInfoModalProps {
    visible: boolean;
    onClose: () => void;
    onSuccess: () => void;
    initialValues: {
        desc: string;
        bizLine: string;
        org: {
            value: number;
            label: string;
        }[];
        user: {
            value: number;
            label: string;
        }[];
    };
    datasetId: number;
}

const ModifyKnowledgeInfoModal: React.FC<ModifyKnowledgeInfoModalProps> = ({
    visible,
    onClose,
    onSuccess,
    initialValues,
    datasetId,
}) => {
    const [form] = Form.useForm();

    const { data: bizLineData } = useRequest(async () => {
        const res = await apiCaller.send('/manage/dataset/bizline', {});
        if (res.code !== 0) return { bizLineList: [] };
        return res.data;
    });

    const { loading: submitting, run: submitModify } = useRequest(
        async (values: ModifyKnowledgeInfoParams) => {
            const res = await apiCaller.send('/manage/dataset/modify', {
                ...values,
                desc: values.desc || '',
            });
            if (res.code !== 0) return;
            message.success('修改信息成功');
            onSuccess();
            onClose();
        },
        { manual: true },
    );

    const handleSubmit = async () => {
        const values = await form.validateFields();

        submitModify({
            ...values,
            datasetId,
            org: undefined,
            user: undefined,
            authOrgIdList: (values.org || []).map(item => item.value),
            authUserList: (values.user || []).map(item => item.value),
        });
    };

    useEffect(() => {
        if (visible) {
            form.setFieldsValue(initialValues);
        }
    }, [visible, initialValues]);

    return (
        <Modal
            title="修改知识库信息"
            open={visible}
            onCancel={onClose}
            onOk={handleSubmit}
            confirmLoading={submitting}
            className="modify-info-modal"
            destroyOnClose
        >
            <Form preserve={false} form={form} layout="vertical">
                <Form.Item label="知识库简介：" name="desc">
                    <Input.TextArea rows={4} placeholder="请输入知识库简介" />
                </Form.Item>
                <Form.Item
                    label="所属业务线"
                    hidden
                    name="bizLine"
                    rules={[{ required: true, message: '请选择业务线' }]}
                >
                    <Select disabled placeholder="请选择业务线">
                        {bizLineData?.bizLineList?.map((item: BizLineItem) => (
                            <Select.Option key={item.code} value={item.code}>
                                {item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item label="所属部门：" name="org">
                    <AutoSearch
                        placeholder="请选择部门"
                        searchUrl="/manage/common/org/search"
                        popupMatchSelectWidth={false}
                        transformOptions={(res: OrgSearchResponse) => {
                            return (res || []).map(item => {
                                return {
                                    ...item,
                                    label: item.orgPathName,
                                    value: item.orgId,
                                };
                            });
                        }}
                    />
                </Form.Item>
                <Form.Item label="授权个人：" name="user">
                    <AutoSearch
                        placeholder="请选择用户"
                        searchUrl="/manage/common/mis/search"
                        transformOptions={(res: MisSearchResponse) => {
                            return (res || []).map(item => {
                                return {
                                    ...item,
                                    label: `${item.name}（${item.mis}）`,
                                    value: item.id,
                                };
                            });
                        }}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ModifyKnowledgeInfoModal;
