.fragment-drawer {
    &__filter {
        margin-bottom: 24px;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 8px;
        width: 100%;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;

        &:hover {
            background: #f0f2f5;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 24px;
        padding: 0 16px;
    }

    &__list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        width: 100%;
    }

    &__item {
        border-radius: 8px;
        background: #fff;
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .ant-card-body {
            padding: 20px;
        }

        &-header {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;
        }

        &-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            line-height: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;

            .anticon {
                font-size: 14px;
                flex-shrink: 0;
            }
        }

        &-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            width: 100%;
            padding-top: 4px;
            border-top: 1px dashed #f0f0f0;

            .ant-tag {
                margin: 0;
                border-radius: 4px;
                padding: 2px 8px;
                font-size: 12px;
                background: #fff7e6;
                color: #d46b08;
                border-color: #ffd591;
            }
        }

        &-content {
            color: #595959;
            line-height: 1.8;
            font-size: 14px;
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 200px;
            overflow-y: auto;
            padding-right: 8px;
            margin-top: 12px;

            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #d9d9d9;
                border-radius: 3px;
            }

            &::-webkit-scrollbar-track {
                background-color: #f5f5f5;
                border-radius: 3px;
            }
        }
    }

    &__pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: 24px;
        padding: 16px 0;
        border-top: 1px solid #f0f0f0;
        align-items: center;
    }

    // 响应式布局
    @media screen and (max-width: 768px) {
        &__filter {
            flex-direction: column;
            align-items: stretch;
        }

        &__list {
            grid-template-columns: 1fr;
        }

        &__item {
            width: 100%;
        }
    }
} 