import React, { useEffect, useRef, useState } from 'react';
import { TreeSelect } from 'antd';
import type { TreeSelectProps } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { ArchiveTreeItem } from '../types';
import { DataNode } from 'antd/es/tree';

interface ArchiveTreeSelectProps extends Omit<TreeSelectProps, 'treeData'> {
    datasetId: number;
    /** 允许选择的层级，不传则默认都可选 */
    levelCanCheck?: number[];
}

interface IDataNode extends DataNode {
    level?: number;
    namePath: string[];
    value: string;
    children?: IDataNode[];
}

// 扩展后端返回数据类型，添加可能存在的children字段
interface ExtendedArchiveTreeItem extends ArchiveTreeItem {
    children?: ExtendedArchiveTreeItem[];
}

const ArchiveTreeSelect: React.FC<ArchiveTreeSelectProps> = ({ datasetId, levelCanCheck, ...props }) => {
    const [treeData, setTreeData] = useState<IDataNode[]>([]);
    const nodesMap = useRef<Record<string, IDataNode>>({});

    const { runAsync: fetchArchiveTree } = useRequest(
        async () => {
            const params = { datasetId: datasetId.toString() };
            const res = await apiCaller.send('/manage/dataset/archive/child', params, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });
            if (res.code !== 0) return [];
            return res.data.children as ExtendedArchiveTreeItem[];
        },
        { manual: true },
    );

    // 递归处理树节点
    const processTreeData = (
        items: ExtendedArchiveTreeItem[],
        level: number,
        parentNamePath: string[] = [],
    ): IDataNode[] => {
        return items.map(item => {
            const namePath = [...parentNamePath, item.content];
            const node: IDataNode = {
                key: String(item.id),
                value: String(item.id),
                title: item.content,
                isLeaf: !item.hasChild,
                disabled: levelCanCheck ? !levelCanCheck.includes(level) : false,
                namePath,
                level,
                children: item.children ? processTreeData(item.children, level + 1, namePath) : undefined,
            };

            // 存储所有节点的引用，方便后续访问
            nodesMap.current[node.value] = node;
            return node;
        });
    };

    useEffect(() => {
        if (!datasetId) return;
        fetchArchiveTree().then(data => {
            const processedData = processTreeData(data, 1);
            setTreeData(processedData);
        });
    }, [datasetId]);

    return (
        <TreeSelect
            placeholder="请选择需要搜索的目录"
            {...props}
            treeData={treeData}
            showSearch
            rootClassName="archive-tree-select-wrapper"
            treeNodeFilterProp="title"
            filterTreeNode={(inputValue, node) => {
                if (levelCanCheck?.length && !levelCanCheck.includes(node.level)) {
                    return false;
                }
                return node.title?.toString().toLowerCase().includes(inputValue.toLowerCase()) || false;
            }}
            onChange={(value, label, extra) => {
                if (props.onChange) {
                    // 处理多选情况
                    if (Array.isArray(value)) {
                        const nodes = value.map(v => nodesMap.current[v]);
                        props.onChange(nodes, label, extra);
                    } else {
                        // 单选情况
                        props.onChange(nodesMap.current[value], label, extra);
                    }
                }
            }}
        />
    );
};

export default ArchiveTreeSelect;
