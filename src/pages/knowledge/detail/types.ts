import { APISpec } from '@mfe/cc-api-caller-pc';

export type KnowledgeDetailItem = APISpec['/manage/dataset/data/list']['response']['datasetWikiInfoList'][0];

// API请求类型
export type KnowledgeDetailRequest = APISpec['/manage/dataset/data/list']['request'];
export type FragmentRequest = APISpec['/manage/dataset/wiki/fragment']['request'];
export type OrgSearchRequest = APISpec['/manage/common/org/search']['request'];
export type MisSearchRequest = APISpec['/manage/common/mis/search']['request'];
// API响应类型
export type BizLineResponse = APISpec['/manage/dataset/bizline']['response'];
export type FragmentResponse = APISpec['/manage/dataset/wiki/fragment']['response'];
export type OrgSearchResponse = APISpec['/manage/common/org/search']['response'];
export type MisSearchResponse = APISpec['/manage/common/mis/search']['response'];
// 业务类型
export type BizLineItem = {
    name: string;
    code: string;
};

export type ArchiveTreeItem = {
    id: number;
    content: string;
    hasChild: boolean;
};

export type ModifyKnowledgeInfoParams = {
    desc: string;
    bizLine: string;
    datasetId: number;
    authOrgIdList: number[];
    authUserList: number[];
};
