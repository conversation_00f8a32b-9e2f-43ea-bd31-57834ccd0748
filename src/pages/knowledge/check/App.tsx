import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';
import React, { useState } from 'react';
import { Button, Input, Form, Spin, Tag, message, Card, Empty, Row, Col, Alert } from 'antd';
import { getWikiIdFromUrl } from '../upload/utils/getWikiIdFromUrl';

// 格式校验结果项类型定义
interface FormatResultItem {
    /** 标题 */
    title: string;
    /** 描述 */
    message: string;
    /** 路径 */
    path: string | null;
    /** 节点ID */
    nodeId: string;
    /** 级别 */
    level: number;
}

// 获取问题级别标签
const getFormatProblemLevel = (level: number) => {
    if (level === 0) {
        return <Tag color="rgb(244, 81, 30)">P0</Tag>;
    } else if (level === 1) {
        return <Tag color="rgb(255, 160, 0)">P1</Tag>;
    } else if (level === 2) {
        return <Tag color="rgb(251, 187, 27)">P2</Tag>;
    } else if (level === 3) {
        return <Tag color="rgb(25, 118, 210)">P3</Tag>;
    }
    return null;
};

const App = () => {
    const [form] = Form.useForm();
    // 移除selectedWikiId和wikiUrl独立状态，改为使用表单状态管理
    const [checkState, setCheckState] = useState<{
        isChecking: boolean;
        result: FormatResultItem[];
        hasChecked: boolean;
    }>({
        isChecking: false,
        result: [],
        hasChecked: false,
    });

    // 格式质检轮询
    const { run: runFormatCheck, cancel: cancelFormatCheck } = useRequest(
        async (wikiId: number) => {
            const res = await apiCaller.send('/manage/dataset/wiki/format/check', { wikiId });

            if (res.code !== 0) {
                cancelFormatCheck();
                setCheckState(prev => ({ ...prev, isChecking: false }));
                message.error('格式检查失败');
                return null;
            }

            if (res.data.finish) {
                cancelFormatCheck();
                setCheckState({
                    isChecking: false,
                    result: (res.data?.infos || []).sort((a, b) => a.level - b.level),
                    hasChecked: true,
                });
            }
        },
        {
            pollingInterval: 1000,
            pollingWhenHidden: false,
            manual: true,
        },
    );

    // 开始检查
    const handleCheck = () => {
        const values = form.getFieldsValue();
        const wikiId = getWikiIdFromUrl(values.wikiUrl);

        if (!wikiId) {
            return;
        }

        setCheckState({
            isChecking: true,
            result: [],
            hasChecked: true,
        });

        runFormatCheck(wikiId);
    };

    // 渲染格式校验结果
    const renderFormatCheckResult = () => {
        if (checkState.isChecking) {
            return <Spin tip="检验中..." rootClassName="wiki-format-check-spin" />;
        }

        if (!checkState.hasChecked) {
            return <Empty description="暂无结果" />;
        }

        if (checkState.hasChecked && !checkState.result.length) {
            return (
                <p style={{ color: '#87d068', display: 'flex', justifyContent: 'center', marginTop: '30px' }}>
                    格式校验无误
                </p>
            );
        }

        return (
            <div className="format-check-result">
                {checkState.result.map(item => (
                    <div key={item.nodeId} style={{ marginBottom: 20 }}>
                        <div
                            style={{
                                fontWeight: 500,
                                fontSize: 18,
                                marginBottom: 2,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                            }}
                        >
                            <div style={{ marginRight: 10 }}>{item.title}</div>
                            {getFormatProblemLevel(item.level)}
                        </div>
                        <div style={{ color: 'rgb(175 175 175)' }}>{item.message}</div>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div className="wiki-format-check-page">
            <Card style={{ marginBottom: 16 }} rootClassName="wiki-format-form-card">
                <Alert
                    message="请给it_bdaiassistant配置管理权限，否则无法进行检验"
                    type="warning"
                    style={{ marginBottom: 10 }}
                />
                <Form form={form} onFinish={handleCheck}>
                    <Row gutter={24}>
                        <Col span={20}>
                            <Form.Item
                                name="wikiUrl"
                                label="Wiki链接"
                                rules={[{ required: true, message: '这是必填项' }]}
                                style={{ flex: 1 }}
                            >
                                <Input
                                    placeholder="请输入文档链接"
                                    onChange={e => {
                                        if (!e.target.value) {
                                            setCheckState({
                                                isChecking: false,
                                                result: [],
                                                hasChecked: false,
                                            });
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={4} style={{ textAlign: 'right' }}>
                            <Form.Item>
                                <Button type="primary" htmlType="submit" loading={checkState.isChecking}>
                                    {checkState.isChecking ? '检查中' : '检查格式'}
                                </Button>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Card>

            <Card title="格式检验结果" rootClassName="wiki-format-check-result-card">
                {renderFormatCheckResult()}
                {form.getFieldValue('wikiUrl') && checkState.result.length > 0 && (
                    <div style={{ marginTop: 16, textAlign: 'center' }}>
                        <Button
                            type="primary"
                            style={{ width: '260px' }}
                            onClick={() => window.open(form.getFieldValue('wikiUrl'), '_blank')}
                        >
                            去修改
                        </Button>
                    </div>
                )}
            </Card>
        </div>
    );
};

export default App;
