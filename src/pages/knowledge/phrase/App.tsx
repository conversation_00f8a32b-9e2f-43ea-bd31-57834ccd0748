import { Button, Col, ConfigProvider, Input, Row, Space, Table } from 'antd';
import { useAntdTable, useBoolean, useDebounceEffect } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useState } from 'react';
import _ from 'lodash';

import ManageSvg from '@src/assets/images/manage.svg?react';
import { ColumnsType } from 'antd/es/table';
import { Data } from '@src/pages/knowledge/types';
import Icon, { ExportOutlined } from '@ant-design/icons';

import './style.scss';
import PhraseDrawer from '@src/pages/knowledge/phrase/phraseDrawer/phraseDrawer';
import usePermissions from '@src/hooks/usePermissions';

const getColumns: (manageFn: (row: Data) => void) => ColumnsType<Data> = manageFn => [
    {
        dataIndex: 'question',
        title: '标准问名称',
    },
    {
        dataIndex: 'phraseSize',
        title: '关联拓展问个数',
        align: 'center',
        width: 138,
    },
    {
        dataIndex: 'id',
        title: '管理关联拓展问',
        align: 'center',
        width: 230,
        render: (id, row) => {
            return (
                <ConfigProvider theme={{ components: { Button: { colorLink: '#222' } } }}>
                    <Button
                        type={'link'}
                        onClick={() => manageFn(row)}
                        icon={<Icon component={() => <ManageSvg className={'icon-manage'} />} />}
                    >
                        管理关联拓展问
                    </Button>
                </ConfigProvider>
            );
        },
    },
];
const App = () => {
    const [isDrawerOpen, { setTrue: openDrawer, setFalse: closeDrawer }] = useBoolean();
    const [currentRow, setCurrentRow] = useState<Data>({} as Data);
    const [searchString, setSearchString] = useState('');
    const { tableProps, data, run, pagination } = useAntdTable(
        async ({ current, pageSize }, searchString: string) => {
            const res = await apiCaller.get(
                '/manage/phrase/standard/list',
                {
                    needExtendSize: true,
                    name: searchString,
                    pageSize,
                    pageNum: current,
                } as any,
                { prefix: '/xianfu/api/bdservice/assistant' },
            );
            if (res.code !== 0) {
                return { list: [], total: 0 };
            }
            return _.pick(res.data, ['list', 'total']);
        },
        { manual: true },
    );

    const update = searchString => run(pagination, searchString);
    useDebounceEffect(
        () => {
            update(searchString);
        },
        [searchString],
        { wait: 500 },
    );

    const [exporting, setExporting] = useState(false);

    const { data: permissions } = usePermissions();
    return (
        <Space direction={'vertical'} style={{ width: '100%' }} size={'middle'}>
            <Row justify={'space-between'}>
                <Col>
                    <Space style={{ fontSize: 20, fontWeight: 500 }}>
                        标准问
                        <span
                            style={{
                                fontSize: 14,
                                fontWeight: 400,
                                color: '#666',
                            }}
                        >
                            共{data?.total || 0}个
                        </span>
                    </Space>
                </Col>
                <Col>
                    <Space>
                        {permissions?.includes('phrase_data_export') ? (
                            <Button
                                loading={exporting}
                                onClick={async () => {
                                    setExporting(true);
                                    const res = await apiCaller.post('/manage/phrase/export', {});
                                    setExporting(false);
                                    if (res.code !== 0) {
                                        return;
                                    }
                                    window.open(res.data);
                                }}
                                icon={<ExportOutlined />}
                            >
                                导出
                            </Button>
                        ) : null}
                        <Input.Search
                            allowClear
                            placeholder={'搜索标准问'}
                            value={searchString}
                            onSearch={() => update(searchString)}
                            onChange={e => setSearchString(e.target.value)}
                        />
                    </Space>
                </Col>
            </Row>
            <Table
                {...tableProps}
                columns={
                    getColumns(row => {
                        setCurrentRow(row);
                        openDrawer();
                    }) as any
                }
                rowKey={'id'}
            />
            <PhraseDrawer
                currentRow={currentRow}
                open={isDrawerOpen}
                onClose={closeDrawer}
                updateList={() => update(searchString)}
            />
        </Space>
    );
};
export default App;
