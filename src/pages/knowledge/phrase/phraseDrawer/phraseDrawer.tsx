import { A<PERSON>, <PERSON><PERSON>, <PERSON>, Drawer, DrawerProps, Form, Pagination, Row, Space, Table } from 'antd';
import { useAntdTable, useBoolean, useResetState } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import EditAndDelete from '@src/pages/knowledge/components/editAndDelete';
import { PlusOutlined } from '@ant-design/icons';
import EditableCell from '@src/pages/knowledge/phrase/phraseDrawer/editableCell';
import { requestHandle } from '@src/pages/knowledge/consts';
import { Data } from '@src/pages/knowledge/types';

interface Props extends DrawerProps {
    currentRow: Data;
    updateList: () => void;
}
interface Phrase {
    id: number | string;
    phrase: string;
}
const TO_CREATE_ID = 'toCreate';
const PhraseDrawer = ({ currentRow, updateList, ...rest }: Props) => {
    const { id: questionId, question } = currentRow;
    const { modal } = App.useApp();
    const { tableProps, data, run, pagination, mutate } = useAntdTable(
        async ({ current, pageSize }, questionId: string | number) => {
            if (!questionId) {
                return { list: [], total: 0 };
            }
            const res = await apiCaller.post('/manage/phrase/relativePhraseList', {
                id: questionId,
                pageSize,
                pageNum: current,
            } as any);
            if (res.code !== 0) {
                return { list: [], total: 0 };
            }
            return _.pick(res.data, ['list', 'total']);
        },
        { manual: true },
    );
    const update = () => {
        run(pagination, questionId);
    };

    useEffect(() => {
        if (questionId) {
            run({ ...pagination, current: 1 }, questionId);
        }
    }, [questionId]);

    const [editingKey, setEditingKey, resetEditingKey] = useResetState('');

    const [form] = Form.useForm();
    const isEditing = (record: Phrase) => String(record.id) === editingKey;
    const startEdit = (record: Partial<Phrase>) => {
        form.setFieldsValue({ phrase: '', ...record });
        setEditingKey(String(record.id));
    };
    const [modifying, setModifying] = useState(false);
    const edit = async (row: Phrase) => {
        setModifying(true);
        const res = await apiCaller.post('/manage/phrase/modify', row as any);
        setModifying(false);
        requestHandle(res, {
            successCallback: _.flow([update, resetEditingKey]),
        });
    };
    const deletePhrase = (id: string) => {
        modal.confirm({
            title: '确认要删除该拓展问吗？',
            content: '删除后无法恢复，是否确认删除？',
            onOk: async () => {
                const res = await apiCaller.post('/manage/phrase/delete', {
                    id,
                });
                requestHandle(res, { successCallback: _.flow([update, updateList]) });
            },
        });
    };
    const create = async (phrase: string) => {
        setModifying(true);
        const res = await apiCaller.post('/manage/phrase/add', {
            id: Number(questionId),
            phrase,
        });
        setModifying(false);
        requestHandle(res, {
            successCallback: _.flow([resetEditingKey, update, endCreating, updateList]),
        });
    };
    const editOrCreate = (row: Phrase) => {
        if (modifying) {
            return;
        }
        const newPhrase = form.getFieldValue('phrase');
        if (row.id === TO_CREATE_ID) {
            return create(newPhrase);
        }
        return edit({ ...row, phrase: newPhrase });
    };

    const [creating, { setTrue: startCreating, setFalse: endCreating }] = useBoolean();
    const createPhrase = () => {
        const newRecord = { id: 'toCreate', phrase: '' };
        startCreating();
        startEdit(newRecord);
    };

    return (
        <Drawer closable={false} width={760} {...rest}>
            <Space direction={'vertical'} style={{ width: '100%' }} size={'middle'}>
                <Row justify={'space-between'}>
                    <Col>
                        <Space>
                            <span style={{ fontSize: 20, fontWeight: 500 }}>关联拓展问</span>
                            <span
                                style={{
                                    color: '#222',
                                }}
                            >
                                ({question})
                            </span>
                            <span
                                style={{
                                    color: '#666',
                                }}
                            >
                                共{data?.total || 0}个
                            </span>
                        </Space>
                    </Col>
                </Row>
                <Form form={form} component={false}>
                    <Table
                        {...tableProps}
                        dataSource={
                            creating
                                ? [{ id: TO_CREATE_ID, phrase: '' }, ...tableProps.dataSource]
                                : tableProps.dataSource
                        }
                        pagination={false}
                        style={{ width: 760 }}
                        components={{
                            body: {
                                cell: EditableCell,
                            },
                        }}
                        columns={[
                            {
                                dataIndex: 'phrase',
                                title: '拓展问名称',
                                onCell: (record: Phrase) => ({
                                    record,
                                    inputType: 'text',
                                    dataIndex: 'phrase',
                                    title: '拓展问名称',
                                    editing: isEditing(record),
                                    onBlur: _.flow([endCreating, resetEditingKey]),
                                    onPressEnter: () => editOrCreate(record),
                                }),
                            },
                            {
                                title: '操作',
                                dataIndex: 'id',
                                align: 'center',
                                width: 92,
                                render: (id, row) => {
                                    return (
                                        <EditAndDelete
                                            editFn={() => startEdit(row)}
                                            deleteFn={() => deletePhrase(id)}
                                        />
                                    );
                                },
                            },
                        ]}
                        rowKey={'id'}
                    />
                </Form>
                <Row justify={'space-between'}>
                    <Button onClick={createPhrase} icon={<PlusOutlined />}>
                        新增扩展问
                    </Button>
                    <Pagination {...pagination} />
                </Row>
            </Space>
        </Drawer>
    );
};
export default PhraseDrawer;
