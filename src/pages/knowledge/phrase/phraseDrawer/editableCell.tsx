import { Form, Input, InputNumber } from 'antd';
import React from 'react';
import { Data } from '@src/pages/knowledge/types';

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
    editing: boolean;
    dataIndex: string;
    title: any;
    inputType: 'number' | 'text';
    record: Data;
    index: number;
    children: React.ReactNode;
    onPressEnter: (data: any) => void;
}

const EditableCell: React.FC<EditableCellProps> = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    onPressEnter,
    onBlur,
    ...restProps
}) => {
    const inputNode =
        inputType === 'number' ? (
            <InputNumber onPressEnter={onPressEnter} onBlur={onBlur} autoFocus />
        ) : (
            <Input onPressEnter={onPressEnter} onBlur={onBlur} autoFocus />
        );

    return (
        <td {...restProps}>
            {editing ? (
                <Form.Item
                    name={dataIndex}
                    style={{ margin: 0 }}
                    rules={[
                        {
                            required: true,
                            message: `请输入${title}!`,
                        },
                    ]}
                >
                    {inputNode}
                </Form.Item>
            ) : (
                children
            )}
        </td>
    );
};
export default EditableCell;
