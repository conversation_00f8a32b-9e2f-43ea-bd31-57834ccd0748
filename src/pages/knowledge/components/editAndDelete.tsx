import { Button, Image, Space } from 'antd';
import editImg from '@src/assets/images/edit.png';
import deleteImg from '@src/assets/images/delete.png';

interface Props {
    data: any;
    deleteFn: (data: any) => void;
    editFn: (data: any) => void;
}
const EditAndDelete = ({ data, deleteFn = () => {}, editFn = () => {} }: Partial<Props>) => {
    return (
        <Space>
            <Button type={'link'} onClick={() => editFn(data)}>
                <Image preview={false} src={editImg} style={{ width: 16, height: 16 }} />
            </Button>
            <Button type={'link'} onClick={() => deleteFn(data)}>
                <Image preview={false} src={deleteImg} style={{ width: 16, height: 16 }} />
            </Button>
        </Space>
    );
};
export default EditAndDelete;
