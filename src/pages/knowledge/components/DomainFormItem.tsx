import { Form, Select } from 'antd';
import { ComponentProps } from 'react';
import { DOMAIN } from '../text';
import { useDomainList } from '../hooks/domainList';

const SelectWithStringValue = ({
    value,
    domainList,
    selectProps,
    ...rest
}: {
    value?: any;
    domainList: any[];
    selectProps: any;
}) => (
    <Select
        value={value ? String(value) : value}
        allowClear
        placeholder={`请选择${DOMAIN}`}
        options={domainList}
        fieldNames={{ label: 'domain', value: 'id' }}
        {...rest}
        {...selectProps}
    />
);
const DomainFormItem = ({
    itemProps = {},
    selectProps = {},
}: {
    itemProps?: ComponentProps<typeof Form.Item>;
    selectProps?: ComponentProps<typeof Select>;
}) => {
    const { data: domainList } = useDomainList();

    return (
        <Form.Item name={'domainId'} label={DOMAIN} {...itemProps}>
            <SelectWithStringValue domainList={domainList as any[]} selectProps={selectProps} />
        </Form.Item>
    );
};
export default DomainFormItem;
