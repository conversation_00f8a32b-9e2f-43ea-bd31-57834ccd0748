import { CSSProperties, useContext, useEffect, useRef, useState } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import createUpload from '@ai/mss-upload-js';
import './editor.scss';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import DisabledContext from 'antd/es/config-provider/DisabledContext';
import { text2delta, addType2Delta } from '@src/pages/knowledge/components/editor/markRender';
import getS3Host from '@src/utils/getS3Host';
import _ from 'lodash';
import { Button, Image, Input, Modal, Space, Upload } from 'antd';
import customQuill from '@src/pages/knowledge/components/editor/customQuill';

const colorPicker = [
    '#000000',
    '#e60000',
    '#ff9900',
    '#ffff00',
    '#008a00',
    '#0066cc',
    '#9933ff',
    '#ffffff',
    '#facccc',
    '#ffebcc',
    '#ffffcc',
    '#cce8cc',
    '#cce0f5',
    '#ebd6ff',
    '#bbbbbb',
    '#f06666',
    '#ffc266',
    '#ffff66',
    '#66b966',
    '#66a3e0',
    '#c285ff',
    '#888888',
    '#a10000',
    '#b26b00',
    '#b2b200',
    '#006100',
    '#0047b2',
    '#6b24b2',
    '#444444',
    '#5c0000',
    '#663d00',
    '#666600',
    '#003700',
    '#002966',
    '#3d1466',
];

const Delta = Quill.import('delta');

customQuill(); // 定制quill组件
const icons = Quill.import('ui/icons') as any;

interface Props {
    value: any;
    onChange: (data: any) => void;
    disabled: boolean;
    readMode: boolean;
    className?: string;
    minHeight?: number;
    defaultDelta?: Record<string, any>;
    style?: CSSProperties;
}
const Quill2Editor = ({
    value: defaultValue,
    onChange = () => {},
    disabled,
    readMode,
    className,
    minHeight = 200,
    defaultDelta,
    style = {},
}: Partial<Props>) => {
    // 是否不可编辑
    const finalDisabled = !readMode ? disabled ?? useContext(DisabledContext) : true;

    // 正在上传图片或视频
    const [spinning, setSpinning] = useState(false);

    // 缓存失焦前的selection
    const [preSelection, setPreSelection] = useState({ index: 0, length: 0 });

    // 图片放大预览
    const [previewVisible, setPreviewVisible] = useState(false);
    const [curImage, setCurImage] = useState('');

    // 强制刷新
    const [updateFlag, setUpdateFlag] = useState(0);

    const update = () => {
        setUpdateFlag(updateFlag + 1);
    };
    // base64转file
    // const base64toFile = dataUrl => {
    //     const arr = dataUrl.split(','),
    //         mime = arr[0].match(/:(.*?);/)[1],
    //         bstr = atob(arr[1]);
    //     let n = bstr.length;
    //     const u8arr = new Uint8Array(n);
    //     while (n--) {
    //         u8arr[n] = bstr.charCodeAt(n);
    //     }
    //     return new File([u8arr], `copyImg${Date.now()}`, { type: mime });
    // };

    const uploadImg = (file, fileType: 'img' | 'video' = 'img', pos) => {
        return new Promise((res, rej) => {
            const uploadInstance = createUpload(
                {
                    onFileInfo: () => {},
                    file,
                    signatureFunc: (() =>
                        apiCaller.get(`${import.meta.env.VITE_API_PREFIX}/assistant/common/s3` as any, {})) as any,
                    bucket: 'bdaiassistant-public',
                    s3_host: getS3Host(),
                    prefix_type: 's3_style',
                    accept: fileType === 'img' ? ['.png', '.jpg'] : ['.mp4'],
                    hashMode: true,
                    onSuccess(fileUrl) {
                        setSpinning(false);
                        if (!quillRef.current) {
                            return;
                        }
                        const quill = quillRef.current; //获取到编辑器本身
                        // 光标位置：用户传入 > getSelection > 缓存的selection > 兜底
                        const cursorPosition = pos ?? (quill?.getSelection()?.index || preSelection?.index || 999999); //获取当前光标位置
                        const link = fileUrl; // 图片链接
                        if (fileType === 'img') {
                            quill.insertEmbed(cursorPosition, 'image', link); //插入图片
                        } else if (fileType === 'video') {
                            quill.insertEmbed(cursorPosition, 'video', link); //插入视频
                        }
                        quill.setSelection(cursorPosition + 1, 0); //光标位置加1
                        res(fileUrl);
                    },
                    onError(errorMsg) {
                        setSpinning(false);
                        rej(errorMsg);
                    },
                    onProgress(percent) {
                        // onProgress({ percent }, file);
                    },
                    onStart() {},
                    onFinish() {},
                    validateFile: () => true,
                },
                1,
            );
            setSpinning(true);
            uploadInstance.upload();
        });
    };

    const quillRef = useRef<Quill | null>(null);
    const quillContainer = useRef<any>(null);

    useEffect(() => {
        if (spinning) {
            return quillRef.current?.disable();
        }
        quillRef.current?.enable();
    }, [spinning]);

    useEffect(() => {
        if (!quillContainer.current || quillRef.current) {
            return;
        }
        quillRef.current = new Quill(quillContainer.current, {
            theme: 'snow',
            placeholder: '在此处补充问题答案',
            modules: {
                uploader: {
                    handler: async (range, uploads) => {
                        const pos = quillRef.current?.getSelection()?.index;
                        quillRef.current?.disable();
                        await uploadImg(uploads[0], 'img', pos);
                        quillRef.current?.enable();
                    },
                },
                toolbar: { container: '#toolbar' },
                clipboard: {
                    matchers: [
                        [
                            Node.ELEMENT_NODE,
                            node => {
                                const textContent = node.textContent || node.href || node.src || '';
                                if (node.tagName === 'IMG') {
                                    return new Delta([{ insert: { image: node.src } }]);
                                } else if (textContent.startsWith('http') && textContent.endsWith('.mp4')) {
                                    return new Delta([{ insert: { video: textContent } }]);
                                } else if (node.tagName !== 'A') {
                                    return new Delta([{ insert: node.textContent }]);
                                }
                                return new Delta([
                                    {
                                        insert: node.textContent,
                                        attributes: { link: node.href },
                                    },
                                ]);
                            },
                        ],
                        [
                            Node.TEXT_NODE,
                            node => {
                                if (node.data.startsWith('http') && node.data.endsWith('.mp4')) {
                                    return new Delta([{ insert: { video: node.data } }]);
                                }
                                return new Delta().insert(node.data);
                            },
                        ],
                    ],
                },
            },
        });

        if (defaultValue) {
            const delta = text2delta(defaultValue);
            quillRef.current?.updateContents(delta);
        }
        if (defaultDelta) {
            quillRef.current?.updateContents(defaultDelta as any);
        }
        quillRef.current?.on('text-change', () => {
            _.flow([
                addType2Delta,
                v => {
                    try {
                        const last = v[v.length - 1];
                        if (typeof last.insert === 'string') {
                            v[v.length - 1].insert = last.insert.trimEnd();
                        }
                        return v;
                    } catch (e) {
                        console.log(e);
                        return v;
                    }
                },
                v => JSON.stringify(v),
                onChange,
            ])(quillRef.current?.getContents());
        });
        quillRef.current?.on('selection-change', data => {
            // 失焦则缓存光标位置
            data && setPreSelection(data);
        });
        finalDisabled && quillRef.current?.disable();
        update(); // quill加载完成后刷新页面
    }, [quillContainer.current]);

    const toolbar = (
        <Space className={'ql-toolbar ql-snow'} style={{ width: '100%' }} id={'toolbar'}>
            {/*<button className={'ql-link'} />*/}
            <Button
                style={{ marginBottom: 4 }}
                onClick={() => {
                    if (!quillRef.current) return;
                    const range = quillRef.current.getSelection();
                    if (range == null || range.length === 0) return;
                    // let preview = quillRef.current.getText(range);
                    // if (/^\S+@\S+\.\S+$/.test(preview) && preview.indexOf('mailto:') !== 0) {
                    //     preview = `mailto:${preview}`;
                    // }
                    let inputValue = '';
                    const modalIns = Modal.confirm({
                        footer: null,
                        icon: null,
                        content: (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <div style={{ width: 140 }}>请输入链接</div>
                                <Input
                                    style={{ height: 30 }}
                                    onChange={v => {
                                        inputValue = v.target.value;
                                    }}
                                />
                                <Button
                                    onClick={() => {
                                        quillRef.current?.format('link', inputValue);
                                        modalIns.destroy();
                                    }}
                                    type={'link'}
                                >
                                    保存
                                </Button>
                            </div>
                        ),
                    });
                    // const { tooltip } = quillRef.current.theme as any;
                    // tooltip.edit('link', preview);
                }}
            >
                <div
                    className={'ql-link ql-formats '}
                    dangerouslySetInnerHTML={{ __html: icons.link }}
                    style={{ width: 18, height: 18 }}
                />
            </Button>
            <Upload
                accept={'.mp4'}
                showUploadList={false}
                customRequest={async data => {
                    const pos = quillRef.current?.getSelection()?.index;
                    quillRef.current?.disable();
                    await uploadImg(data.file, 'video', pos);
                    quillRef.current?.enable();
                }}
            >
                <span
                    className={'ql-video ql-formats '}
                    dangerouslySetInnerHTML={{ __html: icons.video }}
                    style={{ width: 18, height: 18, marginRight: 4 }}
                />
            </Upload>
            <button className="ql-bold"></button>
            <select className="ql-color">
                {colorPicker.map(v => (
                    <option value={v} key={v} />
                ))}
            </select>
            {!finalDisabled ? (
                <div
                    className={'ql-disabledMode'}
                    style={{ marginTop: 4 }}
                    onClick={value => {
                        // 因为上传中会把quill置为不可编辑，所以如果上传中则忽略该操作
                        if (spinning) {
                            return;
                        }
                        const isEnable = quillRef.current?.isEnabled();
                        isEnable ? quillRef.current?.disable() : quillRef.current?.enable();

                        const btn = document.getElementsByClassName('ql-disabledMode')?.[0];
                        if (!btn) return;
                        isEnable ? btn.classList.add('active') : btn.classList.remove('active');
                    }}
                >
                    预览
                </div>
            ) : null}
        </Space>
    );
    return (
        <>
            {!readMode ? toolbar : null}
            <div
                className={className}
                ref={quillContainer}
                style={{ minHeight, ...style }}
                onClick={e => {
                    const target = e.target as any;

                    // 图片预览
                    if (target.tagName !== 'IMG') return;
                    setCurImage(target.src);
                    setPreviewVisible(true);
                }}
            />
            <Image
                key={curImage} // 添加key防止图片切换时UI闪烁
                src={''}
                style={{ display: 'none' }}
                preview={{ src: curImage, visible: previewVisible, onVisibleChange: setPreviewVisible }}
            />
        </>
    );
};
export default Quill2Editor;
