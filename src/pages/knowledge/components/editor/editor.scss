.answer-col {
    .ql-editor {
        width: 368px;
    }
}
.ql-editor {
  img {
    width: 100px;
    height: 100px;
    vertical-align: top; // 处理视频和图片同行视频会高一截的问题
    margin-right: 8px;
  }
}
.ql-video {
    width: 100px;
    height: 100px;
    margin-right: 8px;
}
.ql-video::selection {
    background: #ffc0cb; /* 浅粉色背景 */
    z-index: 1000;
    color: #000; /* 文字颜色 */
}
.ql-tooltip[data-mode=link]::before{
  content: '输入链接'!important;
}
.ql-action::after{
  content: '保存' !important;
}
.ql-tooltip::before {
  content: '链接:' !important;
}
.ql-remove::before {
  content: '移除' !important;
}
.ql-disabled {
    background: rgba(0, 0, 0, 0.04);
}

.ql-disabledMode {
    width: 40px!important;
    cursor: pointer;
    user-select: none;
}
.active {
    background: #f2f2f2!important;
}

.options {
    background: #f2f2f2;
    padding: 10px;
    min-width: 200px;
    border-radius: 5px;
}

.options-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
    cursor: pointer;
}

.buttons {
    display: flex;
}
.buttons-item {
    flex: 1;
    border-radius: 10px;
}
.primary {
    background: #ffdd10;
    border: none;
}
