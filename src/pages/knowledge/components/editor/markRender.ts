import { marked, Token } from 'marked';

const rule = {
    link: {
        test: (str: string) => /\[(.*?)\|(.*?)]/.test(str),
        text2Obj: (str: string) => {
            const value = str.split(/\[(.*?)\|(.*?)]/).filter(v => v);
            return { text: value[0], url: value[1] };
        },
    },
    pic: {
        test: (str: string) => /!\[pic\|.*?]\((.*?)\)/.test(str),
        text2Obj: (str: string) => {
            const value = str.split(/!\[pic\|(.*?)]\((.*?)\)/).filter(v => v);
            return { text: value[0], url: value[1] || value[0] };
        },
    },
    video: {
        test: (str: string) => /!\[video\|.*?]\((.*?)\)/.test(str),
        text2Obj: (str: string) => {
            const value = str.split(/!\[video\|(.*?)]\((.*?)\)/).filter(v => v);
            return { text: value[0], url: value[1] || value[0] };
        },
    },
};

const markedParser = (str: string) => {
    const splitPattern = /(!\[pic\|.*?]\(.*?\))|(!\[video\|.*?]\(.*?\))|(\[.*?\|.*?])/;
    const parts = str.split(splitPattern).filter(v => ![undefined, ''].includes(v));
    const finalStr = parts
        .map(p => {
            if (rule.pic.test(p)) {
                const imgObj = rule.pic.text2Obj(p);
                return `![${imgObj.text}](${imgObj.url})`;
            }
            if (rule.video.test(p)) {
                const linkObj = rule.video.text2Obj(p);
                return `![video|${linkObj.text}](${linkObj.url})`;
            }
            if (rule.link.test(p)) {
                const linkObj = rule.link.text2Obj(p);
                return `[${linkObj.text}](${linkObj.url})`;
            }
            return p;
        })
        .filter((v, i) => {
            if (rule.pic.test(parts[i - 1]) && rule.pic.test(parts[i + 1]) && v === '\n') {
                return false;
            }
            return true;
        })
        .join('');
    const tokens = marked
        .lexer(finalStr, {
            // @ts-ignore
            extensions: {
                inline: [
                    (src, tokens) => {
                        if (/^(!\[video\|.*?]\(.*?\))/.test(src)) {
                            const splitPattern = /!\[video\|.*?]\((.*?)\)/;
                            const raw = src.match(splitPattern)?.[0] || '';
                            const value = raw?.split(splitPattern).filter(Boolean);
                            return { raw, type: 'video', href: value?.[0] };
                        }
                        return undefined;
                    },
                ],
            },
        })
        .map(v => {
            return 'tokens' in v ? v?.tokens : v;
        })
        .flat();
    return tokens;
};

export const parser = (str: string) =>
    (markedParser(str).filter(v => v) as Token[]).map(v => {
        switch (v.type) {
            case 'image':
                return { insert: { image: v.href }, type: 'image' };
            case 'link':
                return { insert: v.text, attributes: { link: v.href }, type: 'link' };
            case 'video':
                return { insert: { video: v.href }, type: 'video' };
            default:
                return { insert: v.raw, type: 'text' };
        }
    });

export const text2delta = (str: string) => {
    try {
        const json = JSON.parse(str);
        if (Array.isArray(json)) {
            return json;
        }
        return parser(str);
    } catch (e) {
        return parser(str);
    }
};

export const addType2Delta = (delta: any[]) => {
    return delta.map(v => {
        if (v.insert.video) {
            return { ...v, type: 'video' };
        } else if (v.insert.image) {
            return { ...v, type: 'image' };
        } else if (v.insert.options) {
            return { ...v, type: 'options' };
        } else if (v.insert.separator) {
            return { ...v, type: 'separator' };
        } else if (v.insert.buttons) {
            return { ...v, type: 'buttons' };
        } else if (typeof v.insert === 'string') {
            if (v.attributes?.link) {
                return { ...v, type: 'link' };
            } else if (v.attributes?.bold || v.attributes?.color) {
                return { ...v, type: 'styledText' };
            }
            return { ...v, type: 'text' };
        }
        return {
            ...v,
            type: 'unknown',
        };
    });
};

export const delta2text = (delta: any) =>
    delta.ops
        .map(v => {
            if (v.insert.video) {
                return `![video|视频](${v.insert.video})`;
            } else if (v.insert.image) {
                return `![pic|图片](${v.attributes?.link || v.insert.image})`;
            } else if (v.attributes?.link) {
                return `[${v.insert}|${v.attributes.link}]`;
            }
            return v.insert;
        })
        .join('')
        .trim();
