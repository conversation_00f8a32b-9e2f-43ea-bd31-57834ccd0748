import { BFFResponse, ErrorBFFResponse, SuccessBFFResponse } from '@mfe/cc-api-caller-pc';
import { message } from 'antd';

export const PREFIX = '/xianfu/api/bdservice/assistant';

interface RequestHandle {
    successCallback: (res: SuccessBFFResponse<Record<string, any>>) => void;
    errorCallback: (res: ErrorBFFResponse) => void;
    errorMsg: string;
    successMsg: string;
    silent: boolean;
}
export const requestHandle = (
    res: BFFResponse<Record<string, any>>,
    {
        successCallback = () => {},
        errorCallback = () => {},
        successMsg = '操作成功',
        silent = false,
    }: Partial<RequestHandle>,
) => {
    if (res.code !== 0) {
        return errorCallback(res);
    }
    successCallback(res);
    return !silent && message.success(res.msg || successMsg);
};
