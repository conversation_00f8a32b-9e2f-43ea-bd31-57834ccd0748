import { Divider, Row, Image, Typography } from 'antd';
import useAssociation from '../../service/association';
import { CSSProperties, Fragment } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import Condition from '@src/components/Condition/Condition';
import useAiStore from '../../data/core';
import AssociationImg from '@src/assets/images/chat/association.png';
import useClientWidth from '../../utils/screenWidth';
interface Props {
    style?: CSSProperties;
}
const Association = ({ style = {} }: Props) => {
    const { associationData, onAssociationPress, setAssociationData } = useAssociation();
    const inputText = useAiStore(v => v.inputText);
    const { getWidth } = useClientWidth();

    if (!associationData?.length) {
        return null;
    }
    return (
        <div
            style={{
                ...style,
                width: getWidth() - 40,
                background: '#fff',
                padding: 10,
                zIndex: 99999,
                borderRadius: '8px 8px 0 0',
            }}
        >
            <Row justify={'space-between'} align={'middle'} style={{ marginBottom: 16 }}>
                <Image src={AssociationImg} preview={false} width={64} />
                <CloseOutlined onClick={() => setAssociationData([])} />
            </Row>
            {associationData?.map((item, index) => {
                return (
                    <>
                        <Typography.Text
                            onClick={() => onAssociationPress(item)}
                            style={{ cursor: 'pointer', color: '#222' }}
                            ellipsis={{ tooltip: item }}
                        >
                            {item.split(inputText).map((it, i, arr) => (
                                <Fragment key={`${it}_${i}`}>
                                    <span>{it}</span>
                                    {i < arr.length - 1 ? <span style={{ color: '#FF6A00' }}>{inputText}</span> : null}
                                </Fragment>
                            ))}
                        </Typography.Text>
                        <Condition condition={[index !== associationData?.length - 1]}>
                            <Divider style={{ marginBottom: 5, marginTop: 5 }} />
                        </Condition>
                    </>
                );
            })}
        </div>
    );
};
export default Association;
