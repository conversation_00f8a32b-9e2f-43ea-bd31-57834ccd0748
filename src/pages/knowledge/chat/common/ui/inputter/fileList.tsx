import useAiStore from '../../data/core';
import { Image, Spin } from 'antd';
import { CloseOutlined, LoadingOutlined } from '@ant-design/icons';
import './style.scss';
import Condition from '@src/components/Condition/Condition';

const FileList = () => {
    const file = useAiStore(v => v.file);
    const setFile = useAiStore(v => v.setFile);

    const handleRemove = (key: string) => {
        setFile(file.filter(f => f.key !== key));
    };

    return (
        <div className="file-list">
            {file.map(f => {
                if (f.type === 'image') {
                    return (
                        <div className="file-list__image-wrapper" key={f.key}>
                            <Condition
                                condition={[f.status === 'uploading', f.status === 'error', f.status === 'success']}
                            >
                                <Spin spinning={true} indicator={<LoadingOutlined spin />}>
                                    <Image src={f.localSrc} width={48} height={48} style={{ objectFit: 'cover' }} />
                                </Spin>
                                <Image src={f.localSrc} width={48} height={48} style={{ objectFit: 'cover' }} />
                                <Image src={f.localSrc} width={48} height={48} style={{ objectFit: 'cover' }} />
                            </Condition>
                            <div className="file-list__image-close">
                                <CloseOutlined onClick={() => handleRemove(f.key)} />
                            </div>
                        </div>
                    );
                }
                return null;
            })}
        </div>
    );
};

export default FileList;
