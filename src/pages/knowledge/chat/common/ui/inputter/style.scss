.file-list {
    max-width: 800px;
    margin: 0 auto 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    &__image-wrapper {
        position: relative;
        border-radius: 8px;
        overflow: hidden;

        &:hover {
            .file-list__image-close {
                opacity: 1;
            }
        }
    }

    &__image-close {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
        background-color: rgba(0, 0, 0, 0.45);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s ease;

        .anticon {
            color: #fff;
            font-size: 12px;
        }

        &:hover {
            background-color: rgba(0, 0, 0, 0.65);
        }
    }

    &__item {
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
        word-break: break-all;
    }

    .ant-image-mask {
        display: none;
    }
}

.with-file {
    border: 1px solid #FFF646;
    background-color: #fff;
    border-radius: 16px;
    padding: 16px;
    .ant-input {
        box-shadow: none !important;
    }
}

.toolbar-container::after, .toolbar-container::before {
    content: '';
    position: absolute;
    border-radius: 0 12px 0 0;
    top: 0;
    right: 0;
    width: 15px;
    height: 100%;
    background-color: #fff;
    z-index: 99;
}

.toolbar-container::before {
    content: '';
    position: absolute;
    border-radius: 12px 0 0 0;
    top: 0;
    left: 0;
    width: 16px;
    height: 100%;
    background-color: #fff;
    z-index: 99;
}