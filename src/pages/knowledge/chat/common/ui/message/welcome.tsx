import React, { useEffect, useState } from 'react';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import BeeGif from 'src/assets/images/chat/icon.gif';
import { Image } from 'antd';
import './welcome.scss';
import useToolbar from '../../service/toolbar';
import useAiStore from '../../data/core';
import useCallerRequest from '../../service/request';
import { EntryPoint } from '../../service/sendMessage/sendMessage';

// 更新类型定义，增加isNew属性
type HotQuestionsType = APISpec['/bee/v2/bdaiassistant/question/hot']['response']['hotQuestion'];

interface WelcomeMessageProps {
    onQuestionClick?: (question: string) => void;
}

const WelcomeMessage: React.FC<WelcomeMessageProps> = ({ onQuestionClick }) => {
    const callerRequest = useCallerRequest();
    const [welcomeText, setWelcomeText] = useState({
        greeting: '',
        weather: '',
    });
    const [hotQuestions, setHotQuestions] = useState<HotQuestionsType>([]);
    const { onToolbarPress } = useToolbar();
    const sessionId = useAiStore(v => v.sessionId);

    const fetchData = async () => {
        try {
            // 并行获取欢迎信息和热门问题
            const [welcomeRes, hotQuestionsRes] = await Promise.all([
                apiCaller.get('/bee/v2/bdaiassistant/common/weather', {}),
                callerRequest.get('/bee/v2/bdaiassistant/question/hot', {}),
            ]);

            // 处理欢迎信息
            if (welcomeRes.code === 0) {
                setWelcomeText({
                    greeting: welcomeRes.data.greeting,
                    weather: welcomeRes.data.weatherTips,
                });
            }
            // 处理热门问题
            if (hotQuestionsRes.code === 0) {
                setHotQuestions(hotQuestionsRes.data.hotQuestions);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
        }
    };

    useEffect(() => {
        if (!sessionId?.length) {
            return;
        }
        fetchData();
    }, [sessionId]);

    const handleQuestionClick = (item, index) => {
        onToolbarPress(
            {
                ...item,
                operationType: Number(item.operationType),
            },
            EntryPoint.option_list + `${index + 1}`,
        );
    };

    return (
        <div className="welcome-message-container">
            <Image src={BeeGif} alt="BeeIcon" className="welcome-icon" preview={false} />
            <div className="welcome-text-container">
                <pre className="welcome-text">{welcomeText?.greeting}</pre>
                <pre className="welcome-text">{welcomeText?.weather}</pre>
            </div>
            <div className="hot-questions-container">
                {(hotQuestions || []).map((item, index) => (
                    <div key={index} className="hot-question-item" onClick={() => handleQuestionClick(item, index)}>
                        {item.link ? (
                            <img
                                src={item.link}
                                alt="hot-question-item"
                                style={{ width: 16, height: 16 }}
                                className="hot-question-item-img"
                            />
                        ) : null}
                        <div className="hot-question-item-content-text">
                            <span>{item.content}</span>
                            {item.isNew && <span className="hot-question-tag">新</span>}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default WelcomeMessage;
