import Condition from '@src/components/Condition/Condition';
import { Image, Row, Spin, Tabs } from 'antd';
import useSendMessage, { EntryPoint, EntryPointType } from '../../service/sendMessage/sendMessage';
import { NewOptions, OperationType, OptionItem, OptionsMessage } from '../../type/message';
import { RightOutlined } from '@ant-design/icons';
import ChangeImg from '@src/assets/images/chat/change.png';
import { CSSProperties, useContext, useState } from 'react';
import { useRequest } from 'ahooks';
import MessageContext from '../message/messageContext';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';

interface Props {
    item: Omit<OptionsMessage, 'insert'> & { insert: { options: NewOptions } };
    style?: CSSProperties;
}
const OptionMessage = ({ item, style }: Props) => {
    const sendMessage = useSendMessage();
    const [tabs, setTabs] = useState(
        (item.insert.options?.tabs || [])?.map(v => ({
            ...v,
            label: v.label,
            key: v.value,
            children: <div></div>,
        })),
    );
    const [pageNum, setPageNum] = useState<number>(2);
    const [options, setOptions] = useState(item.insert.options.options);
    const [hasNext, setHasNext] = useState(item.insert.options.hasNext);
    const [currentTab, setCurrentTab] = useState(tabs?.[0]?.key);

    const msgId = useContext(MessageContext).msgId;

    const callerRequest = useCallerRequest();
    const handleChangeOptions = async (pageNum, name, entryPoint) => {
        // @ts-ignore
        const res = await callerRequest.post('/bee/v1/bdaiassistant/fetchCategoryItems', {
            msgId,
            name,
            pageNum,
            entryPoint,
        });

        if (res.code !== 0) {
            return;
        }
        const resData = res.data as any;
        const content = Array.isArray(resData.currentContent)
            ? resData.currentContent
            : JSON.parse(resData.currentContent);
        const data = content?.find(v => v.type === 'options')?.insert?.options;
        const innerOptions = Array.isArray(data) ? data : data?.options;
        setOptions(innerOptions);
        setPageNum(resData.pageNum);
        setHasNext(resData.hasNext);
    };
    const { run: getData, loading } = useRequest(handleChangeOptions, {
        manual: true,
    });
    const [currentTabIndex, setCurrentTabIndex] = useState(0);
    const changeTab = (value: string, index: number, isNew: boolean) => {
        setCurrentTab(value);
        setCurrentTabIndex(index);
        setPageNum(1);
        getData(1, value, `tab${index + 1}`);
        if (isNew) {
            setTabs(
                tabs.map((tab: any) => {
                    if (tab.key === value) {
                        tab.isNew = false;
                    }
                    return tab;
                }),
            );
        }
    };

    return (
        <div style={style}>
            <Condition condition={[!!tabs?.length]}>
                <Tabs
                    className={'custom-tabs'}
                    tabBarExtraContent={{}}
                    activeKey={currentTab}
                    onChange={e => {
                        setCurrentTab(e);
                        const tab = tabs.find(v => v.key === e);
                        const tabIndex = tabs.findIndex(v => v.key === e);
                        changeTab(e, tabIndex, !!tab?.isNew);
                    }}
                    items={tabs?.map(v => {
                        return {
                            ...v,
                            label: v.isNew ? (
                                <div style={{ position: 'relative' }} key={v.value}>
                                    {v.label}
                                    <div
                                        style={{
                                            position: 'absolute',
                                            top: -3,
                                            right: -8,
                                            background: '#ff192d',
                                            width: 8,
                                            height: 8,
                                            borderRadius: 4,
                                        }}
                                    />
                                </div>
                            ) : (
                                <span key={v.value}>{v.label}</span>
                            ),
                        };
                    })}
                />
            </Condition>
            <div
                style={{
                    width: '100%',
                    background: '#F5F6FA',
                    borderRadius: 8,
                    padding: '12px 8px',
                    minHeight: loading ? 200 : undefined,
                }}
            >
                <Spin spinning={loading}>
                    {options?.map((v: OptionItem, index) => (
                        <div
                            key={v.content}
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                marginBottom: index !== options?.length - 1 ? 16 : 0,
                                cursor: 'pointer',
                            }}
                            onClick={() => {
                                setOptions(
                                    options.map((item: OptionItem) => {
                                        if (item.content === v.content) {
                                            item.isNew = false;
                                        }
                                        return item;
                                    }),
                                );
                                if (v.operationType === OperationType.JUMP_LINK) {
                                    return openLink(v.url);
                                }
                                return sendMessage(v.content, {
                                    abilityType: v.abilityType,
                                    subAbilityType: v.subAbilityType,
                                    entryPointType: EntryPointType.SECTION,
                                    entryPoint: EntryPoint.option_list,
                                });
                            }}
                        >
                            <div style={{ fontSize: 14, color: '#222', position: 'relative' }}>
                                {v.content}
                                <Condition condition={[v.isNew]}>
                                    <div
                                        style={{
                                            borderRadius: 7,
                                            borderBottomLeftRadius: 2,
                                            backgroundColor: '#ff192d',
                                            padding: '0 4px',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            height: 14,
                                            position: 'absolute',
                                            top: -8,
                                            right: -20,
                                            fontSize: 10,
                                            color: '#fff',
                                            lineHeight: '14px',
                                        }}
                                    >
                                        新
                                    </div>
                                </Condition>
                            </div>
                            <RightOutlined style={{ color: '#222', fontSize: 12 }} />
                        </div>
                    ))}
                </Spin>
            </div>
            <Condition condition={[hasNext]}>
                <Row justify={'end'} style={{ marginTop: 5 }}>
                    <div
                        style={{ cursor: 'pointer' }}
                        onClick={() =>
                            getData(
                                pageNum,
                                currentTab,
                                [tabs.length ? `tab${currentTabIndex + 1}` : '', 'refresh'].filter(Boolean).join('-'),
                            )
                        }
                    >
                        <Image src={ChangeImg} preview={false} style={{ marginRight: 3, width: 12.5 }} />
                        <span style={{ color: '#222', fontSize: 14 }}>换一换</span>
                    </div>
                </Row>
            </Condition>
        </div>
    );
};
export default OptionMessage;
