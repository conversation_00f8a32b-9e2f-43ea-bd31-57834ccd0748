.reference-doc {
  border-radius: 8px;
  width: 100%;
  margin-top: 8px;
  border: 1px solid #E5E6EB;
  overflow: hidden;

  .reference-doc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 9px 8px;
    cursor: pointer;
    transition: all 0.3s;
 
    
    &.reference-doc-header-collapsed {
      border-bottom: none;
    }
  }
  
  .reference-doc-title {
    font-size: 12px;
    color: #222;
  }
  
  .reference-doc-icon-group {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 12px;
    
    .reference-doc-book-icon {
      margin-right: 4px;
      color: #3366CC;
    }
    
    .reference-doc-source {
      margin-right: 8px;
    }
    
    .reference-doc-arrow {
      font-size: 12px;
    }
  }

  .reference-doc-list {
    width: 100%;
    padding: 0 9px 8px;
  }

  .reference-doc-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    width: 100%;
  }
  
  .reference-doc-index {
    min-width: 20px;
    font-size: 12px;
  }
  
  .reference-doc-icon-wrapper {
    margin-right: 8px;
  }
  
  .reference-doc-text {
    margin-left: 4px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: #222;
  }
} 