import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Space, Divider, Row } from 'antd';
import './NewPoiWeightingCard.scss';
import { NewPoiWeightingCardItem, NewPoiWeightingCardMessage } from '../../type/message';
import useClientWidth from '../../utils/screenWidth';
import { DownOutlined, UpOutlined } from '@ant-design/icons';

const NewPoiWeightingCard = (props: NewPoiWeightingCardMessage['insert']['newPoiWeightingCard']) => {
    let data: any = [props]; // 旧数据兼容
    if ('list' in props) {
        data = props.list;
    }
    const [collapsed, setCollapsed] = useState(true);
    const [finalData, setFinalData] = useState<NewPoiWeightingCardItem[]>(
        data.slice(0, 3) as NewPoiWeightingCardItem[],
    );
    useEffect(() => {
        setFinalData(collapsed ? data.slice(0, 3) : data);
    }, [collapsed]);

    const getStatusText = (status: number) => {
        switch (status) {
            case 1:
                return '待生效';
            case 2:
                return '生效中';
            case 3:
                return '已使用';
            default:
                return '无意义';
        }
    };

    const getStatusClass = (status: number) => {
        switch (status) {
            case 1:
                return 'pending';
            case 2:
                return 'active';
            case 3:
                return 'expired';
            default:
                return 'default';
        }
    };

    const { getWidth } = useClientWidth();

    return (
        // 减去24px是因为容器有12px的padding
        // 添加最小宽度是因为卡片需要有最小宽度
        <div className="poi-weighting-card" style={{ minWidth: getWidth(0.87) }}>
            <Divider
                style={{
                    margin: '12px -12px',
                    width: 'calc(100% + 24px)',
                }}
            />

            <Space direction="vertical" size={12}>
                {finalData.map((item, index) => {
                    return (
                        <Space align="start" style={{ marginLeft: 16, marginRight: 16 }} key={index}>
                            <div className={`days-circle ${getStatusClass(item.avatar.status)}`}>
                                <div
                                    style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                                >
                                    <span className="number">{item.avatar.totalLimit}</span>
                                    <span className="unit">天</span>
                                </div>

                                <div className={`status-tag ${getStatusClass(item.avatar.status)}`}>
                                    {getStatusText(item.avatar.status)}
                                </div>
                            </div>
                            <div>
                                <div>
                                    <Typography.Text style={{ fontSize: 14, fontWeight: 400 }}>
                                        {item.title}
                                    </Typography.Text>
                                </div>
                                <Space direction="vertical" size={2}>
                                    {item.desc.map((text, index) => (
                                        <Typography.Text key={index} style={{ color: '#999', fontSize: 11 }}>
                                            {text}
                                        </Typography.Text>
                                    ))}
                                </Space>
                            </div>
                        </Space>
                    );
                })}
            </Space>

            {data.length > 3 && (
                <Row justify={'center'} style={{ marginTop: 12 }}>
                    <div
                        onClick={() => setCollapsed(!collapsed)}
                        style={{ fontSize: 12, color: '#666', cursor: 'pointer' }}
                    >
                        {collapsed ? '展开' : '收起'}{' '}
                        {collapsed ? <DownOutlined size={10} /> : <UpOutlined size={10} />}
                    </div>
                </Row>
            )}
        </div>
    );
};

export default NewPoiWeightingCard;
