import React, { useState, useEffect, useRef } from 'react';
import { Input, Space, Typography, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import './FormCard.scss';
import useSendMessage, { EntryPoint } from '../../service/sendMessage/sendMessage';

interface FormCardProps {
    config: {
        label: string;
        type: 'input' | 'radio';
        options?: string[];
        defaultValue?: string;
        tooltip?: string;
        labelWrap?: boolean;
    }[];
    buttonText?: string;
    history?: boolean;
    title?: string;
    subTitle?: string;
    labelSpan?: number;
}

const FormCard: React.FC<FormCardProps> = ({ config, buttonText = '确定', history = false, title, subTitle }) => {
    const [formValues, setFormValues] = useState<Record<string, string>>(
        config.reduce((acc, item) => {
            if (item.defaultValue) {
                acc[item.label] = item.defaultValue;
            }
            return acc;
        }, {} as Record<string, string>),
    );
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [maxLabelWidth, setMaxLabelWidth] = useState(0);
    const labelRefs = useRef<(HTMLDivElement | null)[]>([]);

    useEffect(() => {
        // 计算所有label中最大的宽度
        const calculateMaxWidth = () => {
            const widths = labelRefs.current.filter(Boolean).map(ref => ref?.getBoundingClientRect().width || 0);
            const maxWidth = Math.max(...widths);
            if (maxWidth > 0 && maxWidth !== maxLabelWidth) {
                setMaxLabelWidth(maxWidth);
            }
        };

        calculateMaxWidth();
        // 监听窗口大小变化，重新计算宽度
        window.addEventListener('resize', calculateMaxWidth);
        return () => window.removeEventListener('resize', calculateMaxWidth);
    }, [config]);

    const handleInputChange = (label: string, value: string) => {
        setFormValues(prev => ({
            ...prev,
            [label]: value,
        }));
    };

    const sendMessage = useSendMessage();
    const handleSubmit = () => {
        setIsSubmitted(true);
        const message = Object.entries(formValues)
            .map(([label, value]) => `${label}: ${value}`)
            .join('\n');
        sendMessage(message, { entryPoint: EntryPoint.form_input });
    };

    const isDisabled = history || isSubmitted;

    return (
        <div className="form-card">
            {title && (
                <div className="form-title">
                    <Typography.Title level={5} className="form-title-text">
                        {title}
                    </Typography.Title>
                    <img
                        src="https://s3plus.meituan.net/bdaiassistant-public/rn_assets/common/yellow_ball.png"
                        className="title-decoration"
                        alt="decoration"
                    />
                </div>
            )}
            {subTitle && (
                <Typography.Text type="secondary" className="form-subtitle">
                    {subTitle}
                </Typography.Text>
            )}
            <Space direction="vertical" size={16} style={{ width: '100%' }}>
                {config.map((item, index) => {
                    return (
                        <div key={index} className={`form-item ${item.labelWrap ? 'label-wrap' : ''}`}>
                            <div
                                ref={el => (labelRefs.current[index] = el)}
                                className="form-label-container"
                                style={{
                                    width: maxLabelWidth ? maxLabelWidth : undefined,
                                    marginRight: '16px',
                                }}
                            >
                                <Typography.Text className="form-label">{item.label}</Typography.Text>
                                {item.tooltip && (
                                    <Tooltip title={item.tooltip}>
                                        <QuestionCircleOutlined className="tooltip-icon" />
                                    </Tooltip>
                                )}
                            </div>
                            <div className="form-content" style={{ flex: 1 }}>
                                {item.type === 'input' ? (
                                    <Input
                                        value={formValues[item.label] || ''}
                                        onChange={e => handleInputChange(item.label, e.target.value)}
                                        placeholder={`请输入${item.label}`}
                                        disabled={isDisabled}
                                    />
                                ) : (
                                    <div className="custom-radio-group">
                                        {item.options?.map(option => (
                                            <button
                                                key={option}
                                                type="button"
                                                className={`radio-button ${
                                                    formValues[item.label] === option ? 'active' : ''
                                                }`}
                                                onClick={() => handleInputChange(item.label, option)}
                                                disabled={isDisabled}
                                            >
                                                {option}
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
                {!history && !isSubmitted && (
                    <button onClick={handleSubmit} className="submit-button">
                        {buttonText}
                    </button>
                )}
            </Space>
        </div>
    );
};

export default FormCard;
