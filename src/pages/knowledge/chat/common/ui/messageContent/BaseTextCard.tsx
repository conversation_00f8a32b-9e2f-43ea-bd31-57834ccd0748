import { CSSProperties, useContext } from 'react';
import { Typography } from 'antd';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import MessageContext from '@src/pages/knowledge/chat/common/ui/message/messageContext';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';

interface Props {
    text: string;
    style?: CSSProperties;
    link?: string;
}
const BaseText = ({ text, style, link }: Props) => {
    const Ele = link ? Typography.Link : Typography.Text;
    const { serverId, history } = useContext(MessageContext);
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();
    return (
        <Ele
            style={style}
            href={link}
            onClick={e => {
                e.preventDefault();
                link && openLink(link, serverId, sessionId, bizInfo?.uid, history);
            }}
        >
            {text}
        </Ele>
    );
};
export default BaseText;
