.ql-editor {
    padding: 0;
    font-size: 14px!important;
    a {
        color: #ff6a00;
        text-decoration: none!important;
    }
}

.message-container {
    word-break: break-all;
    ol {
        padding-left: 20px;
    }
    ul {
        padding-left: 20px;
    }
    li {
        margin-top: 2px;
        margin-bottom: 2px;
        line-height: 22px;
    }
    p {
        margin-top: 2px;
        margin-bottom: 2px;
        line-height: 22px;
    }
}

.markdown-table-wrapper {
    width: 100%;
    overflow-x: auto;
    margin: 16px 0;
}

.markdown-table-header {
    width: 100px!important;
}
.markdown-table-cell {
    width: 100px!important;
}
.markdown-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    table-layout: fixed;

    th, td {
        padding: 12px 16px;
        border: 1px solid #e8e8e8;
        text-align: left;
        width: 100px;
        min-width: 100px;
    }

    th {
        background-color: #fafafa;
        font-weight: 500;
    }

    tr {
        &:hover {
            background-color: #fafafa;
        }
    }
}
