import React, { useState } from 'react';
import { Space } from 'antd';
import { Doc } from '../../type/message';
import openLink from '../../utils/openLink';
import KmIcon from '@src/assets/images/chat/km_icon.png';
import ReferCollapse from '@src/assets/images/chat/refer_collapse.png';
import ReferExpand from '@src/assets/images/chat/refer_expand.png';
import './ReferenceDoc.scss';

interface ReferenceDocProps {
    /** 参考来源标题 */
    title: string;
    /** 参考资料列表 */
    list: Doc[];
}

const KmSvg = () => (
    <svg style={{ marginRight: -15 }} width="60" height="18" viewBox="0 0 124 30" xmlns="http://www.w3.org/2000/svg">
        <g id="Page-1" fill="none" fill-rule="evenodd">
            <g id="03.3_\u53CC\u5C42\u5DE5\u5177\u680F\u5907\u4EFD" transform="translate(-24 -9)">
                <g id="Group-3" transform="translate(24 9)">
                    <g id="Combined-Shape" transform="translate(0 4)" fill="#166FF7">
                        <path d="M30.79 14.1v4.44l-20.4 4.28L0 16.2v-4.76l10.38 6.36 20.4-3.7zm-19.1 0l-6.86-4.07V5.65l6.87 3.77L25.24 7v4.46L11.7 14.1z"></path>
                        <path id="Path" d="M13.04 2.05l7.28-.94v3.82L13.04 6.1 9.26 3.9V0z"></path>
                    </g>
                    <path
                        d="M42.66 12.1V9.7h14.25c-.09.69-.27 1.47-.55 2.34h2.34c.26-1.02.43-1.95.5-2.8.1-.94-.28-1.42-1.15-1.42H56.1c.44-.93.79-1.7 1.05-2.31h-2.47c-.19.46-.54 1.23-1.05 2.31h-2.36c-.4-1.12-.7-1.9-.9-2.31h-2.49c.4.94.7 1.71.9 2.31h-3.04c-.38-.92-.72-1.69-1.03-2.31h-2.49c.4.77.75 1.54 1.05 2.31H40.4v4.26h2.27zm7.32 13.09a8.6 8.6 0 0 0 1.07-4.26v-2.1h8.46v-1.95h-8.46v-.5a42.1 42.1 0 0 0 4.5-2.8c.47-.38.65-.8.53-1.29-.1-.47-.43-.72-.98-.76H43.32v1.85h9.09a45.7 45.7 0 0 1-3.7 2.08v1.42h-8.87v1.95h8.87v2.22c0 .62-.09 1.22-.28 1.82-.19.6-.53 1.37-1.03 2.32h2.58zm29.57.2l2.06-1.36a12.27 12.27 0 0 1-2.93-4.09c1.34-2.26 2.18-5.07 2.51-8.44h-2.05a17.74 17.74 0 0 1-1.34 5.7c-.43-1.86-.7-4.28-.78-7.27h4.3V8.05h-.83a24.3 24.3 0 0 0-.63-2.18H77.9c.24.67.45 1.4.64 2.18h-1.58V5.52h-2.25c.02.84.02 1.69.02 2.53h-6.68v8.2c0 1.7-.12 3.22-.35 4.56-.27 1.53-.71 3-1.34 4.38h2.43c.99-2.44 1.49-5.34 1.49-8.72v-2.19h1.68v4.35c0 1.28-.37 2.8-1.1 4.57h2.24c.6-1.55.91-3.11.91-4.7v-4.92c0-.35-.1-.62-.28-.8-.2-.2-.46-.3-.81-.3h-2.64V9.94h4.52c.19 4.28.67 7.61 1.44 9.99-.54.76-1.2 1.45-1.96 2.07l-.7 3.06a14.8 14.8 0 0 0 3.56-2.99c.6 1.17 1.4 2.27 2.4 3.32zm-17.85-2.8l5.66-1.32v-2.05l-1.73.4V11.5h1.73V9.47h-1.73V5.52h-2.19v3.95H61.7v2.03h1.74v8.6l-1.74.39v2.1z"
                        id="\u5B66\u57CE"
                        fill="#212121"
                        fill-rule="nonzero"
                    ></path>
                </g>
            </g>
        </g>
    </svg>
);

const ReferenceDoc: React.FC<ReferenceDocProps> = ({ title = '', list = [] }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const handleClick = (item: Doc) => {
        openLink(item.link);
    };

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <div className="reference-doc">
            <div
                className={`reference-doc-header ${!isExpanded ? 'reference-doc-header-collapsed' : ''}`}
                onClick={toggleExpanded}
            >
                <span className="reference-doc-title">{title}</span>
                <span className="reference-doc-icon-group">
                    <KmSvg />
                    {isExpanded ? <img width={16} src={ReferCollapse} /> : <img width={16} src={ReferExpand} />}
                </span>
            </div>

            {isExpanded && (
                <Space direction="vertical" size={8} className="reference-doc-list">
                    {(list || []).map((item, index) => (
                        <div key={index} className="reference-doc-item" onClick={() => handleClick(item)}>
                            <span className="reference-doc-index">{index + 1}.</span>
                            <img width={13} src={KmIcon} />
                            <span className="reference-doc-text">{item.text}</span>
                        </div>
                    ))}
                </Space>
            )}
        </div>
    );
};

export default ReferenceDoc;
