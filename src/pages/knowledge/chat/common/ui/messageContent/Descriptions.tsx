import { DescriptionsMessage } from '../../type/message';

const Descriptions = ({ list }: DescriptionsMessage['insert']['descriptions']) => {
    return (
        <>
            {list.map((item, index) => (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span className="descriptions-text">{item.label}</span>
                    <span className="descriptions-text">{item.value}</span>
                </div>
            ))}
        </>
    );
};

export default Descriptions;
