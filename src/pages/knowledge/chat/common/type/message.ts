import { CSSProperties } from 'react';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';

interface BaseMessage {
    localId?: string; // 用于本地展示时作为key
    type: string;
}
interface BaseText extends BaseMessage {
    insert: string;
    attributes?: {
        color?: string;
        bold?: boolean;
        link?: string;
    };
}
export interface TextMessage extends BaseText {
    type: 'text';
}
export interface StyledMessage extends BaseText {
    type: 'styledText';
}
export interface LinkMessage extends BaseText {
    type: 'link';
}

export interface VideoMessage extends BaseMessage {
    type: 'video';
    insert: { video: string };
}
export interface ImageMessage extends BaseMessage {
    type: 'image';
    insert: { image: string };
}
export interface MediaMessage extends BaseMessage {
    type: 'media';
    insert: { media: (ImageMessage['insert'] | VideoMessage['insert'])[] };
}

export enum OperationType {
    JUMP_LINK = 1,
    SEND_MESSAGE = 2,
}
export interface OptionItem {
    /** 枚举值，后端返回，透传回后端 */
    abilityType: any;
    /** 枚举值，后端返回，透传回后端 */
    subAbilityType: any;
    /** 枚举值，必填，1：跳转链接，2：发送消息 */
    operationType: OperationType;
    /** 展示内容，必填 */
    content: string;
    /** 可选，operationType为1时的跳链 */
    url: string;
    /** 是否为新 */
    isNew?: boolean;
    /** 图标链接 */
    link?: string;
    /** 是否置顶 */
    top?: boolean;
    /** 链接打开方式，inCurrentTabFull 表示当前页面全屏打开 */
    openWay?: 'inCurrentTabFull' | 'inNewTab';
}
export interface NewOptions {
    options: OptionItem[];
    // 展示在选项列表上面，默认选中第一个
    tabs?: {
        value: any; // 透传回后端用于刷新选项列表
        label: string;
        isNew?: boolean;
    }[];
    hasNext?: boolean;
}
export interface OptionsMessage extends BaseMessage {
    type: 'options';
    insert: {
        options: NewOptions | OptionItem[];
    };
}
export interface SuffixOptionsMessage extends BaseMessage {
    type: 'suffixOptions';
    insert: {
        suffixOptions: {
            options: {
                abilityType: number; // 枚举值，后端返回，透传回后端
                subAbilityType: number; // 枚举值，后端返回，透传回后端
                operationType: 1 | 2; // 枚举值，必填，1：跳转链接，2：发送消息
                content: string; // 展示内容，必填
                url: string;
            }[];
            descriptions: string;
        };
    };
}
export interface SeparatorMessage extends BaseMessage {
    type: 'separator';
    insert: {
        separator: 'nextMessage';
    };
}
export interface ButtonsMessage extends BaseMessage {
    type: 'buttons';
    insert: {
        buttons: {
            text: string;
            url?: string;
            action?: string;
            color?: string;
            type?: 'primary' | 'normal';
        }[];
    };
}

export interface MarkdownMessage extends BaseMessage {
    type: 'markdown';
    insert: {
        markdown: {
            text: string;
        };
    };
}

export interface TableMessage extends BaseMessage {
    type: 'table';
    insert: {
        table: {
            columns: {
                dataIndex: string; // 对应字段的键，如： date, dpData, dpNum
                title: string; // 表格标题，如：日期, 点评数据, 点评量
            }[];
            data: Record<string, string | number>[]; // 如： [{date: '2022-01-02', dpData: 10, dpNum: 20}]
            showCollapse?: boolean; // collapseDesc存在时默认为true，是否展示折叠控件
            collapseDesc?: string; // 折叠控件提示文案
            collapseState?: boolean; // 默认为折叠，折叠控件初始状态（折叠/展开）
            comment?: string; // 表格注释，为空或者undefined则不展示，如：*点评高分规则:评分数大于75,且评价数大于50
            scrollable?: boolean; // 暂不需要；是否允许表格滚动，当列数较多时可能需要
        };
    };
}

export interface UnknownMessage extends BaseMessage {
    type: 'unknown';
    insert: any;
}
export interface CardWithAvatarMessage extends BaseMessage {
    type: 'cardWithAvatar';
    insert: {
        cardWithAvatar: {
            type: string; // 透传给后端用于数据处理
            avatar: string; // 头像，不传则不展示
            title: string; // 超出一行则中间省略
            content: {
                // label和value之间增加冒号展示
                label: string;
                value: string;
                block?: boolean; // 表示是否占满一行，默认一行展示两个数据
            }[];
        };
    };
}

export interface SelectorItemMessage extends BaseMessage {
    // 两行文本，标题+内容
    type: 'selectorItem';
    insert: {
        selectorItem: {
            type: 'reject' | 'poi' | 'poi_private' | 'poi_public'; // 透传给后端，前端不使用，全部类型：reject
            title: string; // 标题，黑色加粗
            content: {
                label: string;
                value: string | number;
                show?: boolean;
                key?: 'label' | 'tag' | 'avatar' | 'online' | 'ID';
            }[]; // 内容，灰色字体
        };
    };
}

export interface SelectorMessage extends BaseMessage {
    type: 'selector';
    insert: {
        selector: {
            content: SelectorItemMessage['insert']['selectorItem'][];
            titleInIm: string; // 聊天页面的title，为空则不展示
            titleInSelector: string; // 驳回原因选择器上的title
            showDivider: boolean; // 控制是否展示分割线
            extendButtonName: string; // 展开按钮文本，为空或者reasons数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
        };
    };
}

export interface SystemMessage extends BaseMessage {
    type: 'systemMessage';
    insert: {
        systemMessage: {
            content: string;
        };
    };
}

export interface Config extends BaseMessage {
    type: 'config';
    insert: {
        config: {
            style: CSSProperties;
        };
    };
}

export interface NewPoiWeightingCardItem {
    title: string;
    desc: string[];
    avatar: {
        status: number; // 0：无意义 1：待生效 2：生效中 3：已使用
        totalLimit: number;
    };
}
export interface NewPoiWeightingCardMessage extends BaseMessage {
    type: 'newPoiWeightingCard';
    insert: {
        newPoiWeightingCard: NewPoiWeightingCardItem | { list: NewPoiWeightingCardItem[] };
    };
}
export interface FormMessage extends BaseMessage {
    type: 'form';
    insert: {
        form: {
            config: {
                label: string;
                type: 'radio' | 'input';
                options?: string[]; // input不需要
                defaultValue?: string; // 默认值
            }[];
            buttonText?: string; // 按钮文案，默认为确定
        };
    };
}

export interface AdditionFile {
    key: string;
    type: 'image' | 'file';
    src?: string;
    localSrc: string;
    status: 'uploading' | 'success' | 'error';
    metaData?: any;
}
export interface AdditionMessage extends BaseMessage {
    // 只出现在question中
    type: 'addition';
    insert: {
        addition: {
            additionList: AdditionFile[];
        };
    };
}

export interface CollapsibleTextMessage extends BaseMessage {
    type: 'collapsibleText';
    insert: {
        collapsibleText: {
            content: (TextMessage | LinkMessage)[];
            extendButtonName: string; // 展开按钮文本，为空则不展示按钮
            maxHeight?: number; // 最大高度，超出则省略并展示展开按钮
        };
    };
}

export interface ThinkContentMessage extends BaseMessage {
    // 所有ThinkContent合并展示，status以最后一个ThinkContent的status作为思考是否结束的标志
    type: 'thinkContent';
    insert: {
        thinkContent: {
            status: 'thinking' | 'done';
            content: string;
        };
    };
}

export interface DescriptionsMessage extends BaseMessage {
    type: 'descriptions';
    insert: {
        descriptions: {
            list: {
                label: string;
                value: string;
            }[];
        };
    };
}

export interface Doc {
    type: 'km' | 'meituan';
    text: string;
    link: string;
}
export interface ReferenceDocMessage extends BaseMessage {
    type: 'referenceDoc';
    insert: {
        referenceDoc: {
            title: string;
            list: Doc[];
        };
    };
}

export interface TitleMessage extends BaseMessage {
    type: 'title';
    insert: {
        title: {
            title: string;
            subTitle?: string;
        };
    };
}

export enum MessageStatus {
    generating, //对应服务器状态未完成
    done, //对应服务器状态已完成
    sending, // 其余为本地状态
    typing,
    error,
    stopped,
}

export enum MessageFrom {
    left,
    right,
    middle,
}
export type Message =
    | TextMessage
    | StyledMessage
    | LinkMessage
    | VideoMessage
    | ImageMessage
    | SystemMessage
    | MarkdownMessage
    | MediaMessage
    | OptionsMessage
    | Config
    | CardWithAvatarMessage
    | SuffixOptionsMessage
    | ButtonsMessage
    | SelectorMessage
    | SelectorItemMessage
    | TableMessage
    | NewPoiWeightingCardMessage
    | FormMessage
    | CollapsibleTextMessage
    | AdditionMessage
    | ThinkContentMessage
    | DescriptionsMessage
    | ReferenceDocMessage
    | TitleMessage;

export const enum FeedbackType {
    like = 1,
    dislike,
    block_answer,
    tt,
}
export type MessageData = {
    data: Message[];
    typingData?: Message[]; // 打字动画需要的当前数据
    id: string; // 问题需要先渲染才能拿到id，所以需要本地生成随机值作为id；回答的id直接用服务器的id
    serverId?: string; // 优先取服务器数据的msgId，其次取questionMsgId，用于和服务器交互
    status?: MessageStatus.generating | MessageStatus.done | MessageStatus.error; // 服务器状态
    localStatus: MessageStatus; // 本地状态
    isStopped?: boolean; // 是否已取消会话
    from: MessageFrom;
    noFooter?: boolean;
    feedbackType?: FeedbackType;
    suffixOptions?: SuffixOptionsMessage['insert']['suffixOptions'];
    config?: Config['insert']['config'];
    history?: boolean;
    retryParams?: Parameters<ReturnType<typeof useSendMessage>>; // 重试参数，用于快速重新发起请求
};
