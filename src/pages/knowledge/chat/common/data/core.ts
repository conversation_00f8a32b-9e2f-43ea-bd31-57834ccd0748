import { create } from 'zustand';
import message, { MessageStateAndActions } from './message';
import config, { ConfigStateAndActions } from './config';
import event, { EventStateAndActions } from './event';
import file, { FileStateAndActions } from './file';

type AllStateAndActions = MessageStateAndActions & ConfigStateAndActions & EventStateAndActions & FileStateAndActions;

const getStoreEle =
    (set, get) =>
    <T extends keyof AllStateAndActions>(key: T) => {
        return get()[key] as AllStateAndActions[T];
    };
const setStoreEle = (set, get) => (newState: Partial<AllStateAndActions>) => {
    set({ ...get(), ...newState });
};

const useAiStore = create<
    AllStateAndActions & { getStoreEle: ReturnType<typeof getStoreEle>; setStoreEle: ReturnType<typeof setStoreEle> }
>((set, get) => ({
    ...message.getActions(set, get),
    ...message.defaultState,
    ...config.getActions(set, get),
    ...config.defaultState,
    ...event.defaultState,
    ...event.getActions(set, get),
    ...file.defaultState,
    ...file.getActions(set, get),
    getStoreEle: getStoreEle(set, get),
    setStoreEle: setStoreEle(set, get),
}));
export default useAiStore;
