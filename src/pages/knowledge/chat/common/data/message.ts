import { MessageData, MessageStatus } from '../type/message';
import { ConfigStateAndActions } from './config';
import { EventKey, EventStateAndActions } from './event';
import _ from 'lodash';

interface State {
    messageArray: MessageData[]; // 消息数据
    pollingTimer: NodeJS.Timeout | null; // 当前拉取回答消息的timer，用于中止回答
    pollingMessageLocalId: string | null; // 最新的回答消息的id，用于中止回答消息，
    typingTimer: NodeJS.Timeout | null; // 打字动画的timer，用于中止回答
    dragged: boolean; // 是否开始过手动滚动屏幕，此时拉取消息后不再需自动滚动到屏幕底部，防止打断用户操作导致不好的用户体验，拉取消息开始时会将该标志置为否
    scrollToEnd: () => void; // 滚动到屏幕底部的方法，在UI处注册
    inputText: string; // 输入文本，方便其他组件监听或者改变输入框数据
    sessionId: string[];
    onOpenPoiSelector: () => void;
    isInFastMode: boolean; // 是否处于快速模式中
    /** 是否已经输入过消息 */
}

const defaultState: State = {
    messageArray: [],
    pollingTimer: null,
    pollingMessageLocalId: null,
    typingTimer: null,
    dragged: false,
    scrollToEnd: () => {},
    onOpenPoiSelector: () => {},
    inputText: '',
    sessionId: [],
    isInFastMode: false,
};

const getActions = (set: Setter, get: Getter) => ({
    setOnOpenPoiSelector: (onOpenPoiSelector = () => {}) => {
        set({
            onOpenPoiSelector,
        });
    },
    getLatestSessionId: () => {
        return _.last(get().sessionId);
    },
    appendSessionId: (sessionId: string) => {
        if (!sessionId) {
            console.log('sessionId为空');
        }
        set({
            sessionId: [...get().sessionId, sessionId],
        });
    },
    setPollingMessageLocalId: (pollingMessageLocalId: string | null) => {
        set({
            pollingMessageLocalId,
        });
    },
    setInputText: (inputText: string) => {
        set({
            inputText,
        });
    },
    setDragged: (dragged: boolean) => {
        set({
            dragged,
        });
    },
    setScrollToEnd: (scrollToEnd: () => void) => {
        set({
            scrollToEnd: () => {
                try {
                    if (!get().dragged) {
                        scrollToEnd();
                    }
                } catch (e) {
                    console.error(e);
                }
            },
        });
    },
    setPollingTimer: (timer: NodeJS.Timeout | null) => {
        set({
            pollingTimer: timer,
        });
    },
    // 屏蔽是否需要打字动画的差异
    getMessageArray: () => {
        if (get().typing.needPlay) {
            return get().messageArray.map(v => ({ ...v, data: v.typingData }));
        }
        return get().messageArray;
    },
    appendMessage: (message: MessageData | MessageData[], position: 'before' | 'after' = 'after') => {
        const isBefore = position === 'before';
        if (Array.isArray(message)) {
            set({
                messageArray: isBefore ? [...message, ...get().messageArray] : [...get().messageArray, ...message],
            });
            return;
        } else {
            set({
                messageArray: isBefore ? [message, ...get().messageArray] : [...get().messageArray, message],
            });
        }

        // 如果向后添加消息则滚动到底部
        if (position === 'after') {
            setTimeout(() => {
                get().scrollToEnd();
            }, 200);
        }

        // 自动变更pollingMessageLocalId
        if (position === 'after') {
            get().setPollingMessageLocalId(message.id);
        }

        // 执行回调
        get().executeListeners(message, EventKey.MESSAGE_APPEND);
    },
    modifyMessage: (
        targetId?: MessageData['id'],
        newMessage?: Partial<MessageData> | ((currentMessage: Partial<MessageData>) => Partial<MessageData>),
    ) => {
        if (!targetId) return;
        const messageArray = get().messageArray;
        const index = messageArray.findIndex(v => v.id === targetId);
        if (index === -1) {
            return;
        }
        const targetEle = messageArray[index];
        const finalNewMessage =
            typeof newMessage === 'function' ? newMessage(targetEle) : { ...targetEle, ...newMessage };

        if (finalNewMessage?.status === MessageStatus.done) {
            // 如果不需打字动画或者最终的回答为空则直接置为done，非这两种否则会在打字动画的逻辑中置为done
            if (!get().typing.needPlay || !finalNewMessage?.data?.length) {
                finalNewMessage.localStatus = MessageStatus.done;
            }
            if (!finalNewMessage?.data?.length && !targetEle.data.length) {
                finalNewMessage.data = [
                    {
                        type: 'text',
                        insert: finalNewMessage.isStopped
                            ? '用户已取消会话'
                            : '非常抱歉服务器去送外卖了，再问一句试试呀',
                    },
                ];
            }
        }

        messageArray[index] = {
            ...targetEle,
            ...finalNewMessage,
        };
        set({
            messageArray: [...messageArray],
        });

        // 执行回调
        get().executeListeners({ originalMessage: targetEle, newMessage }, EventKey.MESSAGE_MODIFY);
    },
    appendMessageContent: (targetId: MessageData['id'], content: MessageData['data']) => {
        get().modifyMessage(targetId, targetEle => ({
            ...targetEle,
            data: [...(targetEle.data || []), ...content],
        }));
    },
    deleteMessage: (targetId: MessageData['id']) => {
        set({
            messageArray: get().messageArray.filter(v => v.id !== targetId),
        });
    },
    // 不需打字动画的消息不会执行该逻辑
    // 数据会在数据拉取到就处理好
    // 标准问或者多轮数据中的文本数据会被切分成单字符，所以每次输出一个对象即可
    // markdown数据每个对象为一个token，所以也应每次直接输出下个对象，尤其是处理表格等单token含字符较多的数据时应直接输出token
    startTypingMessage: (targetId: string) => {
        const typing = get().typing;
        const charTypingAnimationDuration = get().isInFastMode
            ? typing.fastCharTypingAnimationDuration
            : typing.charTypingAnimationDuration;

        const typingTimer = setInterval(() => {
            const currentMessage = get().messageArray.find(v => v.id === targetId); // 获取最新的数据
            const scrollToEnd = get().scrollToEnd;

            if (!currentMessage?.data.length) {
                // 还未拉取到数据
                return;
            }

            const typingData = currentMessage?.typingData || [];
            const typingDataLength = typingData.length;
            let targetContent = currentMessage?.data[typingDataLength - 1];

            // 如果流式结果已完全返回且打字动画未完成，则切换到快速模式
            if (currentMessage?.status === MessageStatus.done && !get().isInFastMode) {
                console.log('🐝：switch to fast mode🚀🚀🚀');
                set({
                    isInFastMode: true,
                });
                clearInterval(typingTimer);
                get().startTypingMessage(targetId);
                return;
            }

            // 打字动画完成的动作
            if (
                currentMessage?.status === MessageStatus.done && // 服务器数据已接收完成
                typingDataLength === currentMessage.data.length // 打字动画已完成
            ) {
                set({
                    isInFastMode: false,
                });
                clearInterval(typingTimer); // 必须先clear
                get().stopPollingMessage();
                get().modifyMessage(targetId, { localStatus: MessageStatus.done });
                return;
            }

            targetContent = currentMessage?.data[typingDataLength];
            if (!targetContent) {
                return;
            }
            typingData.push(targetContent);

            get().modifyMessage(targetId, { typingData });
            scrollToEnd();
        }, charTypingAnimationDuration);
        set({
            typingTimer,
        });
    },

    stopPollingMessage: (isStopped?: boolean) => {
        const { pollingMessageLocalId, modifyMessage, pollingTimer, typingTimer, setPollingMessageLocalId } = get();

        // 停止轮询
        pollingTimer && clearTimeout(pollingTimer);

        // 停止打字动画
        typingTimer && clearInterval(typingTimer);
        set({
            isInFastMode: false,
        });

        // 修改消息状态，影响footer展示
        pollingMessageLocalId &&
            modifyMessage(pollingMessageLocalId, {
                localStatus: MessageStatus.done,
                status: MessageStatus.done,
                isStopped,
            });

        setPollingMessageLocalId(null);

        // 自动滚动到底部
        setTimeout(get().scrollToEnd, 200);
    },
    existPollingMessage: () => {
        const latestLocalId = get().pollingMessageLocalId;
        const latestMessage = get().messageArray.find(v => v.id === latestLocalId);
        return (
            latestLocalId &&
            (latestMessage?.status === MessageStatus.generating || // 正在拉取服务器数据
                latestMessage?.localStatus === MessageStatus.generating) // 正在执行打字动画
        );
    },
    // 判断当前是否有消息正在生成ing
    isGenerating: () => {
        return get().existPollingMessage();
    },
});
export type MessageStateAndActions = State & ReturnType<typeof getActions> & ConfigStateAndActions;
type Setter = (v: Partial<State>) => void;
type Getter = () => MessageStateAndActions & EventStateAndActions;

export default { getActions, defaultState, key: 'message' };
