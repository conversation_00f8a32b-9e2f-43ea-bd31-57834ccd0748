import { MessageData } from '../type/message';
import _ from 'lodash';

export enum EventKey {
    MESSAGE_APPEND = 'message_append',
    MESSAGE_MODIFY = 'message_modify',
    API = 'api',
}

export const DEFAULT_ALL_ID = 'default_all_id';

const defaultState = {
    listeners: {
        [EventKey.MESSAGE_APPEND]: [] as {
            callback: (data?: MessageData | MessageData[]) => void;
            id: string;
            localListenerId: string;
        }[],
        [EventKey.MESSAGE_MODIFY]: [] as {
            callback: (originData?: MessageData, targetData?: MessageData) => void;
            id: string;
            localListenerId: string;
        }[],
        [EventKey.API]: [] as { callback: (data: any) => void; id: string; localListenerId: string }[],
    },
};
type State = typeof defaultState;

const getActions = (set: Setter, get: Getter) => ({
    executeListeners: (params: any, eventKey: EventKey, id: string | typeof DEFAULT_ALL_ID = DEFAULT_ALL_ID) => {
        const listeners = get().listeners;
        let targetListeners = listeners[eventKey].filter(item => item.id === id).map(v => v.callback);
        if (id === DEFAULT_ALL_ID) {
            targetListeners = listeners[eventKey].map(v => v.callback);
        }

        let errorFlag = true;
        targetListeners.forEach(callback => {
            try {
                callback(params);
            } catch (e) {
                errorFlag = false;
                console.log('回调事件出错', { eventKey, id, params, e });
            }
        });
        return errorFlag;
    },
    addListener: (eventKey: EventKey, id: string | typeof DEFAULT_ALL_ID, callback: (v: State) => void) => {
        const listeners = get().listeners;
        const localListenerId = _.uniqueId('listener_');
        set({
            listeners: {
                ...listeners,
                [eventKey]: [...listeners[eventKey], { id, callback, localListenerId }],
            },
        });
        return () => {
            const listeners = get().listeners;
            set({
                listeners: {
                    ...listeners,
                    [eventKey]: listeners[eventKey].filter(item => item.localListenerId !== localListenerId),
                },
            });
        };
    },
});
export type EventStateAndActions = State & ReturnType<typeof getActions>;
type Setter = (v: Partial<State>) => void;
type Getter = () => EventStateAndActions;

export default { getActions, defaultState, key: 'config' };
