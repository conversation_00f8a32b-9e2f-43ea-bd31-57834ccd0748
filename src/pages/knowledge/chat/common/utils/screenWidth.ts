import { useSize } from 'ahooks';

const DefaultWidth = 800; // 默认容器最大宽度
const useClientWidth = () => {
    const size = useSize(document.querySelector('body'));
    const width = size?.width || DefaultWidth;
    const fullScreen = new URLSearchParams(window.location.search).get('fullScreen');
    return {
        getWidth: (targetWidth?: number) => {
            // 小窗口或者指定了全屏则使用页面容器宽度作为小蜜容器的宽度
            const finalWidth = width < 768 || fullScreen ? width : DefaultWidth;
            if (!targetWidth) {
                return finalWidth;
            }
            if (targetWidth < 1) {
                return targetWidth * finalWidth;
            }
            return (targetWidth / DefaultWidth) * finalWidth; // 等比缩放
        },
    };
};
export default useClientWidth;
