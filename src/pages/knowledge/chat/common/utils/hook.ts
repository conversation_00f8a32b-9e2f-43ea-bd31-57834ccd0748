import { useEffect, useState, useMemo, useRef } from 'react';

export interface BizInfo {
    bizId: number;
    tenantId: number;
    bizName: string;
    tenantName: string;
}

export const useCountdown = (options: { milliseconds: number; interval?: number; onEnd?: () => void }) => {
    const { milliseconds, interval = 1000, onEnd } = options;

    const target = useMemo(() => +new Date() + milliseconds, [milliseconds]);
    const [timeLeft, setTimeLeft] = useState(milliseconds);

    const onEndRef = useRef(onEnd);
    onEndRef.current = onEnd;

    useEffect(() => {
        const timer = setInterval(() => {
            const next = target - +new Date();

            if (next <= 0) {
                onEndRef.current?.();
                clearInterval(timer);
            }

            setTimeLeft(next);
        }, interval);
        return () => clearInterval(timer);
    }, [target, interval]);

    return [timeLeft];
};
