import { FeedbackData, RateLevel } from '../types';
import React, { CSSProperties, useState } from 'react';
import RateItem from './RateItem';
import checkedImg from '@src/assets/images/checked.png';
import unCheckedImg from '@src/assets/images/unChecked.png';
import { Input, Row } from 'antd';

type FeedbackConfig = any;
interface RateContainer {
    configs: FeedbackConfig;
    data: FeedbackData;
    onChange: (p: FeedbackData) => void;
}
const styles: Record<string, CSSProperties> = {
    stars: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    container: {
        flex: 1,
        paddingLeft: 12,
        paddingRight: 12,
    },
    spanarea: {
        borderWidth: 0.5,
        borderRadius: 10.5,
        borderColor: '#EEEEEE',
        paddingLeft: 12,
        paddingRight: 12,
        paddingTop: 7,
        paddingBottom: 7,
        marginBottom: 15,
        marginTop: 8,
    },
    rest: {
        marginRight: 12,
        marginBottom: 7,
        color: '#d6d6d6',
    },
};

const customSelectStyles = {
    lineSelected: {
        borderWidth: 0.5,
        borderColor: '#FFE233',
        backgroundColor: 'rgba(255,221,26,0.10)',
    },
    lineSelectedspan: {
        fontSize: 12,
        color: '#222',
        fontWeight: '500',
    },
    lineSelect: {
        borderWidth: 0.5,
        borderColor: 'rgba(209,211,216,0.60)',
    },
    lineSelectspan: {
        color: '#666666',
        fontSize: 12,
    },
};

const RateContainer = (props: RateContainer) => {
    const config = props.configs.find(item => item.value === props.data.star);

    const [selectedTips, setSelectedTips] = useState<string[]>(props.data.tips);
    const onChange = (p: Partial<FeedbackData>) => {
        setSelectedTips(p.tips || []);
        props.onChange({
            ...props.data,
            ...p,
        });
    };

    const onRateChange = (payload: { star: RateLevel; desc: string }) => {
        props.onChange({
            ...payload,
            comment: '',
            tips: [],
        });
    };

    const onTipSelected = (t: string) => {
        const exist = props.data.tips.find(tip => tip === t);

        if (exist) {
            onChange({ tips: props.data.tips.filter(tip => tip !== t) });
        } else {
            onChange({ tips: [...props.data.tips, t] });
        }
    };

    const showTipSelect = !!config && config.tips.length;
    const showComment = !!config && config.showComment;

    return (
        <div style={styles.container}>
            <div style={styles.stars}>
                {props.configs.map(c => (
                    <RateItem {...c} key={c.value} current={props.data.star} onPress={onRateChange} />
                ))}
            </div>

            {showTipSelect ? (
                <Row justify={'space-between'}>
                    {config.tips.map(it => {
                        return (
                            <div
                                className={'pointer'}
                                key={it}
                                onClick={() => onTipSelected(it)}
                                style={{
                                    padding: 4,
                                    borderRadius: 6,
                                    border: '1px solid #E8E8E8',
                                    marginTop: 8,
                                    width: '30%',
                                    background: selectedTips.includes(it) ? '#FFFCED' : undefined,
                                    fontSize: 12,
                                    textAlign: 'center',
                                    position: 'relative',
                                }}
                            >
                                <image
                                    href={selectedTips.includes(it) ? checkedImg : unCheckedImg}
                                    style={{
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        width: 13,
                                        height: 13,
                                        zIndex: 10000,
                                    }}
                                />
                                {it}
                            </div>
                        );
                    })}
                </Row>
            ) : null}

            {/*{showTipSelect ? (*/}
            {/*    <WithTheme compName="select" themeStyles={selectStyles} styles={{ ...customSelectStyles }}>*/}
            {/*        {selectStyles => (*/}
            {/*            <Select*/}
            {/*                styles={{ containerWrap: { justifyContent: 'flex-start' } }}*/}
            {/*                key={props.data.star}*/}
            {/*                items={config.tips.map(value => ({*/}
            {/*                    value,*/}
            {/*                    label: value,*/}
            {/*                }))}*/}
            {/*                onPress={it => onTipSelected(it.value as string)}*/}
            {/*                renderItem={(item, selected) => (*/}
            {/*                    <div*/}
            {/*                        style={[*/}
            {/*                            { backgroundColor: '#fff' },*/}
            {/*                            selected ? selectStyles.lineSelected : selectStyles.lineSelect,*/}
            {/*                            item.disabled ? selectStyles.disabledSelect : {},*/}
            {/*                            {*/}
            {/*                                width: 108,*/}
            {/*                                height: 34,*/}
            {/*                                justifyContent: 'center',*/}
            {/*                                alignItems: 'center',*/}
            {/*                                position: 'relative',*/}
            {/*                                borderRadius: 6.5,*/}
            {/*                            },*/}
            {/*                        ]}*/}
            {/*                    >*/}
            {/*                        <Image*/}
            {/*                            source={selected ? checkedImg : unCheckedImg}*/}
            {/*                            style={{ position: 'absolute', left: 0, top: 0, width: 13, height: 13 }}*/}
            {/*                        />*/}
            {/*                        <div style={[selectStyles.selectItem, { borderRadius: 12 }]}>*/}
            {/*                            <span*/}
            {/*                                style={[*/}
            {/*                                    selectStyles.lineSelectspan,*/}
            {/*                                    selected ? customSelectStyles.lineSelectedspan : { fontSize: 12 },*/}
            {/*                                ]}*/}
            {/*                            >*/}
            {/*                                {item.label}*/}
            {/*                            </span>*/}
            {/*                        </div>*/}
            {/*                    </div>*/}
            {/*                )}*/}
            {/*            />*/}
            {/*        )}*/}
            {/*    </WithTheme>*/}
            {/*) : null}*/}

            {showComment ? (
                <Input.TextArea
                    value={props.data.comment}
                    onChange={comment => onChange({ comment: comment.target.value })}
                    maxLength={100}
                    style={styles.spanarea}
                    placeholder="帮助我们更好的提升，您的意见很重要"
                    // renderRest={(maxLength, wordCount, _, s) => {
                    //     return (
                    //         <span style={{ ...s, ...styles.rest, ...(wordCount >= 100 ? { color: 'red' } : {}) }}>
                    //             {wordCount} / {maxLength}
                    //         </span>
                    //     );
                    // }}
                />
            ) : null}
        </div>
    );
};

export default RateContainer;
