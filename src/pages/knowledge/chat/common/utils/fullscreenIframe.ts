/**
 * 在当前页面打开 iframe，占据页面下方 80% 的空间
 * @param url 需要在 iframe 中打开的 URL
 */
export const openFullscreenIframe = (url: string): void => {
    // 创建全屏容器
    const fullscreenContainer = document.createElement('div');
    fullscreenContainer.id = 'assistant-fullscreen-container';
    fullscreenContainer.style.position = 'fixed';
    fullscreenContainer.style.bottom = '0';
    fullscreenContainer.style.left = '0';
    fullscreenContainer.style.width = '100%';
    fullscreenContainer.style.height = 'calc(100% - 48px)';
    fullscreenContainer.style.backgroundColor = '#fff';
    fullscreenContainer.style.zIndex = '9999';
    fullscreenContainer.style.boxShadow = '0 -2px 10px rgba(0, 0, 0, 0.1)';
    fullscreenContainer.style.borderTopLeftRadius = '8px';
    fullscreenContainer.style.borderTopRightRadius = '8px';

    // 创建 loading 状态
    const loadingContainer = document.createElement('div');
    loadingContainer.id = 'iframe-loading';
    loadingContainer.style.display = 'flex';
    loadingContainer.style.flexDirection = 'column';
    loadingContainer.style.alignItems = 'center';
    loadingContainer.style.justifyContent = 'center';
    loadingContainer.style.width = '100%';
    loadingContainer.style.height = '100%'; // 减去头部高度
    loadingContainer.style.position = 'absolute';
    loadingContainer.style.backgroundColor = '#fff';
    loadingContainer.style.zIndex = '1';

    // 创建 loading 图标
    const loadingSpinner = document.createElement('div');
    loadingSpinner.style.width = '40px';
    loadingSpinner.style.height = '40px';
    loadingSpinner.style.border = '4px solid #f3f3f3';
    loadingSpinner.style.borderTop = '4px solid #3498db';
    loadingSpinner.style.borderRadius = '50%';
    loadingSpinner.style.animation = 'spin 1s linear infinite';

    // 添加旋转动画
    const style = document.createElement('style');
    style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    document.head.appendChild(style);

    // 创建 loading 文本
    const loadingText = document.createElement('div');
    loadingText.textContent = '加载中...';
    loadingText.style.marginTop = '12px';
    loadingText.style.color = '#666';

    loadingContainer.appendChild(loadingSpinner);
    loadingContainer.appendChild(loadingText);
    fullscreenContainer.appendChild(loadingContainer);

    // 创建 iframe
    const iframe = document.createElement('iframe');
    iframe.src = url;
    iframe.style.width = '100%';
    iframe.style.height = 'calc(100% - 40px)'; // 减去头部高度
    iframe.style.border = 'none';
    iframe.style.position = 'relative';
    iframe.style.zIndex = '0';

    // iframe 加载完成后隐藏 loading
    iframe.onload = () => {
        const loadingElement = document.getElementById('iframe-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    };

    fullscreenContainer.appendChild(iframe);

    // 添加到 body
    document.body.appendChild(fullscreenContainer);
};

export const closeFullscreenIframe = (): void => {
    const fullscreenContainer = document.getElementById('assistant-fullscreen-container');
    // 移除全屏容器
    if (fullscreenContainer && document.body.contains(fullscreenContainer)) {
        document.body.removeChild(fullscreenContainer);
    }
};
