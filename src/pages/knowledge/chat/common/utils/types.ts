// 评价弹窗相关类型
export const enum RobotType {
    MOSES,
    INTELLIGENCE,
}

export const enum RateLevel {
    ONE = 1,
    TWO,
    THREE,
    FOUR,
    FIVE,
}

export interface FeedbackData {
    star: RateLevel;
    tips: string[];
    comment: string;
    desc: string;
}

export const enum SubmitStatus {
    FERESH,
    DOING,
    DONE,
}

// 向外暴露，不使用const enum
export enum Source {
    PROFILE = 'bee_my_help', // 蜜蜂主页
    POI_DETAIL = 'bee_poi_detail', // 商家详情页
    WORKBENCH = 'bee_workbench', // 蜜蜂工作台
    UNKNOWN = 'unknown', // 未知source的fallback
}
