import { AdditionFile, AdditionMessage } from '@src/pages/knowledge/chat/common/type/message';

export const getAdditionMessage = (file: AdditionFile[], text?: string) => {
    if (!file.length) {
        return '';
    }
    return JSON.stringify(
        [
            {
                type: 'addition',
                insert: {
                    addition: {
                        additionList: file.filter(item => item.status === 'success'),
                    },
                },
            } as AdditionMessage,
            text
                ? {
                      type: 'text',
                      insert: text,
                  }
                : null,
        ].filter(Bo<PERSON>an),
    );
};
