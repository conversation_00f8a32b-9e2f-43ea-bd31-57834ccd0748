export const isSupportWideScreen = () => {
    const source = new URLSearchParams(window.location.search).get('source');
    // 如果当前是非 iframe 嵌入场景则不支持全屏
    if (window.parent === window) {
        return false;
    }
    if (!source) {
        return true;
    }
    return ['xianfu_web', 'performance_web'].includes(source);
};

export const isSupportClose = () => {
    if (window.parent === window) {
        return false;
    }
    const source = new URLSearchParams(window.location.search).get('source');
    if (!source) {
        return true;
    }
    return ['xianfu_web', 'performance_web'].includes(source);
};
