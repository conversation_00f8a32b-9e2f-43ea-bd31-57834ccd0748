import React, { CSSProperties, useEffect, useState } from 'react';
import { RateLevel } from './types';
import { useCountdown } from './hook';
import submit from '@src/assets/images/submit.png';
import positive from '@src/assets/images/positive.png';
import negative from '@src/assets/images/negative.png';
import { Image } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

interface FeedbackResult {
    star: RateLevel;
    onCancel: () => void;
}

const enum FeedbackStatus {
    FRESH,
    POSITIVE,
    NEGATIVE,
}

const ConfigMap = {
    [FeedbackStatus.POSITIVE]: {
        img: positive,
        t1: '谢谢您的评价, 我们会努力做到更好!',
    },
    [FeedbackStatus.NEGATIVE]: {
        img: negative,
        t1: '很抱歉带来不好的体验',
    },
};

const styles: Record<string, CSSProperties> = {
    container: {
        display: 'flex',
        alignItems: 'center',
        height: 300,
        flexDirection: 'column',
    },
    image: {
        marginBottom: 24,
        width: 120,
        height: 110,
    },
    span1: {
        fontSize: 16,
        color: '#222426',
        textAlign: 'center',
    },
    spanCountDown: {
        marginTop: 12,
        color: 'rgba(0,0,0,0.50)',
        textAlign: 'center',
        fontSize: 12,
    },
};

const FeedbackResult = (props: FeedbackResult) => {
    const feedbackStatus = props.star <= RateLevel.ONE ? FeedbackStatus.NEGATIVE : FeedbackStatus.POSITIVE;

    const config = ConfigMap[feedbackStatus];
    const [img, setImg] = useState(config.img);
    useEffect(() => {
        setTimeout(() => setImg(submit), 1000);
    }, []);
    const [coutdown] = useCountdown({
        milliseconds: 3000,
        onEnd: props.onCancel,
    });

    return (
        <div style={styles.container}>
            <div
                style={{
                    display: 'flex',
                    width: '100%',
                    height: 89,
                    justifyContent: 'flex-end',
                    alignItems: 'flex-start',
                }}
            >
                <CloseOutlined onClick={props.onCancel} style={{ marginRight: 28, marginTop: 28 }} />
            </div>
            <Image src={img} style={styles.image} preview={false} />
            <div style={{ alignItems: 'center', marginTop: -20 }}>
                <span style={styles.span1}>{config.t1}</span>
                <span style={styles.spanCountDown}>{Math.round(coutdown / 1000)}秒后自动关闭</span>
            </div>
        </div>
    );
};

export default FeedbackResult;
