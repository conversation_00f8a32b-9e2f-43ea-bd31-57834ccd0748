.c_666 {
    color: #666;
}
.c_999 {
    color: #666;
}
.s_12 {
    font-size: 12px;
}
.s_14 {
    font-size: 14px;
}
.s_20 {
    font-size: 20px;
}
.pointer {
    cursor: pointer;
}
.h_center {
    display: flex;
    align-items: center;
}
.v_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.mt_10 {
    margin-top: 10px;
}
.no-padding {
    .ant-modal-content {
        padding: 0;
    }
}
.custom-tabs {
    .ant-tabs-tab-btn[aria-selected='true'] {
        color: #222 !important;
    }
    .ant-tabs-tab-btn[aria-selected='false'] {
        color: #666 !important;
    }
}
.ant-btn-primary {
    color: #fff;
    background-color: #000;
    -color: #000;
    border-radius: 2px !important;
    box-shadow: none !important;
    &:hover {
        color: #fff !important;
        background-color: #7a7a7a !important;
        border-color: #7a7a7a !important;
    }
}
.ant-btn {
    border-radius: 2px !important;
}
.ant-modal-confirm-paragraph {
    max-width: unset!important;
}

.table-message {
    font-size: 12px;
    .ant-table-cell {
        font-size: 12px!important;
    }
}

.custom-assistant-rule-confirm-btn {
    outline: none !important;
}

.toolbar-item-container {
    column-gap: 0 !important;
    height: 38px !important;

    .toolbar-item-content {
        font-size: 12px !important;
        font-weight: 400;
        line-height: 24px;
        gap: 0;
    }
}

.toolbar-container-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -8px;
    width: 24px;
    height: 36px;
    pointer-events: none;
    background: linear-gradient(270deg, #e0e0e0 0%, #F5F5F900 100%);
    // background: red;
    filter: blur(4px);
}