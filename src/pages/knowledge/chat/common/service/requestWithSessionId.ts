import { useRequest } from 'ahooks';
import { useState, useEffect } from 'react';
import { apiCaller, APICallerConfig } from '@mfe/cc-api-caller-pc';
import useAiStore from '../data/core';

/**
 * 等待 sessionId 可用后发起请求的 Hook
 * @param method HTTP 方法
 * @param path 请求路径
 * @param params 请求参数（会自动注入 sessionId）
 * @param config 请求配置
 * @param options useRequest 的配置选项
 */
export const useRequestWithSessionId = (
    path: string,
    params: any = {},
    config: APICallerConfig<any> = {},
    options: Parameters<typeof useRequest>[1] = {},
): any => {
    // 监听 sessionId 变化
    const [sessionId, setSessionId] = useState('');
    useEffect(() => {
        const unsubscribe = useAiStore.subscribe(state => {
            const latestSessionId = state.sessionId[state.sessionId.length - 1];
            if (latestSessionId && latestSessionId !== sessionId) {
                setSessionId(latestSessionId);
            }
        });
        return unsubscribe;
    }, [sessionId]);

    return useRequest(
        async () => {
            // 自动注入 sessionId
            const requestParams = { ...params, sessionId };
            const res = await apiCaller.send(path, requestParams, { ...config });
            if (res.code === 0) {
                return res.data;
            }
        },
        {
            ...options,
            // 只有当 sessionId 存在时才发起请求
            ready: !!sessionId && options.ready !== false,
        },
    );
};

export default useRequestWithSessionId;
