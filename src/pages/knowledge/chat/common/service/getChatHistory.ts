import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import { VERSION_PARAMS_2 } from '@src/pages/knowledge/chat/common/service/openSession';
import {
    Config,
    Message,
    MessageData,
    MessageFrom,
    MessageStatus,
    SuffixOptionsMessage,
} from '@src/pages/knowledge/chat/common/type/message';
import _ from 'lodash';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';

import { adaptServerMessageComponents } from '@src/pages/knowledge/chat/common/service/sendMessage/adaptServerMessage';

export const adaptMessageArray = (data: any): MessageData[] => {
    return data
        .map(v => {
            let answerMessageData;
            try {
                answerMessageData = JSON.parse(v.currentContent) as Message[];
                // 数字字符串不会产生错误，但不符合预期
                if (typeof answerMessageData === 'number') {
                    throw new Error('currentContent是字符串型数字');
                }
            } catch (e) {
                // 解析失败则直接展示
                answerMessageData = [{ insert: v.currentContent, type: 'text' }];
            }
            if (!answerMessageData?.length) {
                return;
            }
            let data = [
                ...adaptServerMessageComponents(answerMessageData).map(m => ({
                    ...m,
                    localId: _.uniqueId('messageContent_'), // 添加本地唯一key，用于UI组件指定key
                })),
            ];
            // suffixOptions & config处理
            const suffixOptions: SuffixOptionsMessage | undefined = data.find(
                v => v.type === 'suffixOptions',
            ) as SuffixOptionsMessage;
            const config: Config | undefined = data.find(v => v.type === 'config') as Config;
            data = data.filter(v => v.type !== 'suffixOptions' && v.type !== 'config');

            return {
                ...v,
                serverId: v.msgId || v.questionMsgId,
                history: true,
                from: v.type === 2 ? MessageFrom.left : MessageFrom.right,
                data,
                typingData: data,
                localStatus: MessageStatus.done,
                suffixOptions: suffixOptions?.insert?.suffixOptions,
                config: config?.insert?.config,
            };
        })
        .filter(Boolean);
};

let minMsgId;
let noMore = false;
const useGetChatHistory = () => {
    const callerRequest = useCallerRequest();
    const appendMessage = useAiStore(state => state.appendMessage);

    return async () => {
        if (noMore) {
            return;
        }
        const res = await callerRequest.post('/bee/v1/bdaiassistant/getChatHistory', { minMsgId, ...VERSION_PARAMS_2 });
        if (res.code !== 0) {
            return;
        }

        minMsgId = res.data.minMsgId;

        if (!res?.data?.msgItems?.length) {
            noMore = true;
            appendMessage({
                localStatus: MessageStatus.done,
                from: MessageFrom.middle,
                id: _.uniqueId('message_'),
                data: [
                    {
                        insert: '没有更多啦',
                        type: 'text',
                        localId: _.uniqueId('messageContent_'),
                    },
                ],
            });
            return;
        }

        const recordList = adaptMessageArray(res.data.msgItems);
        appendMessage(
            recordList.map(v => ({ ...v, id: _.uniqueId('message_') })),
            'before',
        );
        appendMessage(
            {
                localStatus: MessageStatus.done,
                from: MessageFrom.middle,
                id: _.uniqueId('message_'),
                history: true,
                data: [
                    {
                        insert: '点击查看更多',
                        type: 'text',
                        localId: _.uniqueId('openSession_'),
                    },
                ],
            },
            'before',
        );
    };
};
export default useGetChatHistory;
