import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useAsyncEffect } from 'ahooks';
import { useState } from 'react';
import { getUrlState } from '@src/hooks/useUrlState';

// 缓存键名
const GRAY_CACHE_KEY = 'bdai_interaction_gray_cache';

// 记录接口请求状态
let isApiRequesting = false;
let apiRequestPromise: Promise<boolean> | null = null;

const getGrayCache = () => {
    try {
        const cacheStr = sessionStorage.getItem(GRAY_CACHE_KEY);

        if (!cacheStr) {
            return null;
        }

        const parsed = JSON.parse(cacheStr);

        // 检查缓存是否过期（5分钟）
        const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟，单位毫秒

        const now = Date.now();
        if (now - parsed.timestamp > CACHE_EXPIRE_TIME) {
            // 缓存过期，清除缓存
            sessionStorage.removeItem(GRAY_CACHE_KEY);
            return null;
        }

        return parsed;
    } catch (error) {
        console.error('获取灰度缓存失败', error);
        return null;
    }
};

const setGrayCache = (interactionGray: boolean) => {
    try {
        const cacheData = {
            timestamp: Date.now(),
            interactionGray,
        };

        sessionStorage.setItem(GRAY_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
        console.error('设置灰度缓存失败', error);
    }
};

// 获取灰度信息的核心方法
const fetchGrayInfo = async (): Promise<boolean> => {
    // 如果已经在请求中，返回已有的Promise
    if (isApiRequesting && apiRequestPromise) {
        return apiRequestPromise;
    }

    // 标记开始请求
    isApiRequesting = true;

    apiRequestPromise = new Promise<boolean>(resolve => {
        // 封装异步逻辑
        const executeAsync = async () => {
            try {
                const getGrayFormNet = async () => {
                    // 发起接口请求
                    const res = await apiCaller.get('/bee/v2/bdaiassistant/common/gray', {});

                    if (res.code !== 0) {
                        console.warn('接口请求失败，code:', res.code);
                        return true;
                    }

                    // 更新缓存
                    const isGray = !!res.data.interactionGray;
                    setGrayCache(isGray);
                    return isGray;
                };
                // 尝试从缓存获取
                // PS：恢复页面会保持sessionStorage导致数据一直不更新
                const grayCache = getGrayCache();
                if (grayCache && typeof grayCache.interactionGray === 'boolean') {
                    resolve(grayCache.interactionGray);
                    return;
                }

                // 缓存不存在或已过期，从网络获取
                const isGray = await getGrayFormNet();
                resolve(isGray);
            } catch (error) {
                console.error('获取灰度信息失败', error);
                resolve(true); // 出错时默认返回true
            } finally {
                // 标记请求完成
                isApiRequesting = false;
            }
        };

        // 执行异步函数
        executeAsync();
    });

    return apiRequestPromise;
};

// 是否是新版本灰度用户
const useReleaseGray = () => {
    const [isReleaseGray, setIsReleaseGray] = useState<boolean>(true);
    const query = getUrlState();

    useAsyncEffect(async () => {
        // 如果是强制使用旧版本，直接返回 false
        if (query?.forceUseOldRelease) {
            setIsReleaseGray(false);
            return;
        }
        if (query?.forceUseNewRelease) {
            setIsReleaseGray(true);
            return;
        }

        // 获取灰度信息
        const isGray = await fetchGrayInfo();
        setIsReleaseGray(isGray);
    }, []);

    return isReleaseGray;
};

export default useReleaseGray;
