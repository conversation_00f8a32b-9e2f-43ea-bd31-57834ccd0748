import { useRequest } from 'ahooks';
import useCallerRequest from './request';

const useSceneTips = () => {
    const callerRequest = useCallerRequest();
    const { data } = useRequest(
        async () => {
            const res = await callerRequest.get('/bee/v2/bdaiassistant/scene/tips', {});
            if (res.code === 0) {
                return res.data?.sceneTipsMap as any | undefined;
            }
            return {} as any | undefined;
        },
        { cacheKey: 'sceneTips' },
    );

    return {
        sceneTips: data,
    };
};

export default useSceneTips;
