import { useRequest } from 'ahooks';
import useCallerRequest from './request';

export type EventType = 'trigger';
const useTrace = () => {
    const callerRequest = useCallerRequest();
    const { run: trace } = useRequest(
        async (entryPoint: string, eventType: EventType, content?: string) => {
            await callerRequest.post('/bee/v1/bdaiassistant/trace/log', {
                entryPoint,
                eventType,
                content,
            });
        },
        { manual: true },
    );

    return trace;
};
export default useTrace;
