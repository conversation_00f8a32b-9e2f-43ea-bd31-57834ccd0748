import { useEffect, useRef } from 'react';
interface SourceConfigType {
    sourceName: string;
    needPoiSelector: boolean;
}

const DefaultSource = 'xianfu_web';

export const useSourceConfig = () => {
    const sourceConfig = useRef<SourceConfigType>({
        sourceName: 'Hi，我是小蜜智能助手',
        needPoiSelector: true,
    });
    const query = new URLSearchParams(window.location.search) || DefaultSource;
    useEffect(() => {
        switch (query.get('source') || DefaultSource) {
            case 'liansuo_web':
                sourceConfig.current = {
                    sourceName: 'Hi，我是小蜜智能助手-连锁特别版',
                    needPoiSelector: false,
                };
                break;
            default:
                break;
        }
    }, []);
    return sourceConfig.current;
};

const useSourceParams = () => {
    // extra: JSON.stringify({href: 'xxx'})
    const sourceParams = useRef<{ source?: string; extra?: string } & Record<`aichat_scene_${string}`, string>>({});
    useEffect(() => {
        const query = new URLSearchParams(window.location.search) || DefaultSource;
        sourceParams.current.source = query.get('source') || DefaultSource;
        const extraString = query.get('extra');
        if (extraString) {
            sourceParams.current.extra = extraString;
        }
        for (const [key, value] of query.entries()) {
            // 处理过的参数不再处理
            if (!['source', 'extra'].includes(key)) {
                sourceParams.current[key] = value;
            }
        }
    }, []);

    return sourceParams.current;
};
export default useSourceParams;
