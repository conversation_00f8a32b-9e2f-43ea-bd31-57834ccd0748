import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';

const useInstruction = () => {
    const fetchInstruction = async () => {
        const res = await apiCaller.get('/bee/v1/bdaiassistant/getInstructions', {});

        if (res.code !== 0) {
            return;
        }

        return res.data.instructions;
    };

    const { data: instruction = '' } = useRequest(fetchInstruction, {
        cacheKey: 'instruction',
        staleTime: 1000 * 60 * 10,
    });
    return instruction;
};
export default useInstruction;
