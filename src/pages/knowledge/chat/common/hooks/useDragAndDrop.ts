import { useState } from 'react';
import { useImageUpload } from './useImageUpload';

interface UseDragAndDropProps {
    onDrop?: (e: React.DragEvent) => void;
}

interface UseDragAndDropResult {
    isDragging: boolean;
    dragProps: {
        onDragEnter: (e: React.DragEvent) => void;
        onDragOver: (e: React.DragEvent) => void;
        onDragLeave: (e: React.DragEvent) => void;
        onDrop: (e: React.DragEvent) => void;
    };
}

export const useDragAndDrop = ({ onDrop }: UseDragAndDropProps = {}): UseDragAndDropResult => {
    const [isDragging, setIsDragging] = useState(false);
    const { handleUpload } = useImageUpload();

    const handleDragEnter = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        onDrop?.(e);

        const file = e.dataTransfer.files[0];
        handleUpload(file);
    };

    return {
        isDragging,
        dragProps: {
            onDragEnter: handleDragEnter,
            onDragOver: handleDragOver,
            onDragLeave: handleDragLeave,
            onDrop: handleDrop,
        },
    };
};
