import { render } from '@src/module/root';
import Config from '@src/pages/knowledge/components/config';
import { Space, Typography } from 'antd';
import { useEffect } from 'react';
import openAssistant from '@src/pages/knowledge/chat/common/utils/openAssistant';

const App = () => {
    const from: any = new URLSearchParams(window.location.search).get('from') || 'tt';
    useEffect(() => {
        openAssistant(from);
    }, []);
    return (
        <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column', paddingTop: 20 }}>
            <Space direction={'vertical'} align={'center'}>
                <div>
                    <Typography.Text>下载蜜蜂，体验完整功能:</Typography.Text>
                    <Typography.Link href={'https://igate.waimai.meituan.com/igate/beedeploy#/download/v2'}>
                        点我下载
                    </Typography.Link>
                </div>
                <div>
                    <Typography.Text>跳转网页版小蜜智能助手:</Typography.Text>
                    <Typography.Link href={`/page/bdservice/knowledge/chat?source=${from}_web`}>
                        点我跳转
                    </Typography.Link>
                </div>
            </Space>
        </div>
    );
};
render(
    <Config>
        <App />
    </Config>,
    '智能助手中转页',
);
