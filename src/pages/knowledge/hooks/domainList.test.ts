import { describe, expect, test, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';

const data = {
    domainList: [
        {
            id: 1,
            domain: '资质驳回',
        },
        {
            id: 1001,
            domain: '测试域1',
        },
    ],
};

describe('useDomainList', () => {
    test('should fetch domain list', async () => {
        const mockResponse = {
            code: 0,
            data,
        };

        vi.doMock('@mfe/cc-api-caller-pc', async () => {
            return {
                apiCaller: {
                    get: () => Promise.resolve(mockResponse),
                },
            };
        });

        // 动态import是为了使apiCaller的mock生效
        const { useDomainList } = await import('./domainList');

        // Render the hook
        const { result, rerender } = renderHook(() => useDomainList());

        expect(result.current.data).toEqual(undefined);

        // 等待某个条件成立，比如状态更新
        await waitFor(
            () => {
                expect(result.current.data).not.toBeUndefined();
            },
            { timeout: 2000 },
        );

        expect(result.current.data).toHaveLength(mockResponse.data.domainList.length);
    });
});
