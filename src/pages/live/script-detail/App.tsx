import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import Title from '@src/components/Title';
import { useQuery } from '@src/hooks/useQuery';
import { useAntdTable, useRequest } from 'ahooks';
import { Button, Table, TableColumnType, Typography, message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

type ScriptDetail = APISpec['/livescript/task/detail']['response'];
type Item = APISpec['/livescript/task/skuList']['response']['data'][number];

const enum ResultStatus {
    DOING = 0,
    FAILED,
    SUCCESS,
}

const StatusMap = new Map<number, string>([
    [ResultStatus.DOING, '生成中'],
    [ResultStatus.FAILED, '生成失败'],
    [ResultStatus.SUCCESS, '生成成功'],
]);
const App = () => {
    const { query } = useQuery();
    const [detail, setDetail] = useState<ScriptDetail>();

    const fetchDetail = async () => {
        const res = await apiCaller.send('/livescript/task/detail', {
            id: query.id,
        });

        if (res.code !== 0) {
            return;
        }

        setDetail(res.data);
    };

    const fetchScriptSkus = async ({ current, pageSize }) => {
        const res = await apiCaller.send('/livescript/task/skuList', {
            taskId: query.id,
            pageNum: current,
            pageSize,
        });

        if (res.code !== 0) {
            return {
                total: 0,
                list: [],
            };
        }

        return {
            total: res.data.total || 0,
            list: res.data.data,
        };
    };

    const { search, tableProps, loading } = useAntdTable(fetchScriptSkus, {
        manual: true,
    });

    const markRead = (id?: string) => {
        apiCaller.send('/livescript/task/detail/viewed', { id }, { silent: true });
    };

    useEffect(() => {
        if (!query.id) {
            return;
        }

        markRead(query.id);
        fetchDetail();
        search.reset();
    }, [query.id]);

    const onExport = async () => {
        const res = await apiCaller.send('/livescript/task/export', {
            id: query.id,
        });

        if (res.code !== 0) {
            return;
        }

        message.success('正在为您导出，请留心大象公众号的通知');
    };

    const { run, loading: exporting } = useRequest(onExport, { manual: true });

    const columns: TableColumnType<Item>[] = [
        {
            dataIndex: 'id',
            width: 100,
            title: '商品id',
        },
        {
            dataIndex: 'skuName',
            width: 100,
            title: '商品名',
        },
        {
            dataIndex: 'poiName',
            width: 100,
            title: '商家名',
        },
        {
            dataIndex: 'resultStatus',
            width: 100,
            title: '生成结果',
            render: t => StatusMap.get(t),
        },
        {
            dataIndex: 'resultStatusDesc',
            width: 100,
            title: '异常原因',
        },
        {
            dataIndex: 'content',
            title: '产品介绍部分',
        },
    ];

    return (
        <div>
            <div style={{ marginBottom: 16 }}>
                <Title style={{ marginBottom: 0, marginTop: 24 }}>我的脚本方案</Title>
                <Typography.Text type="secondary">
                    {dayjs(detail?.createTime).format('YYYY.MM.DD HH:mm')}
                </Typography.Text>
            </div>
            <Table {...tableProps} columns={columns} loading={loading} />

            <div style={{ textAlign: 'right' }}>
                <Button type="primary" onClick={run} loading={exporting}>
                    下载至电脑
                </Button>
            </div>
        </div>
    );
};

export default App;
