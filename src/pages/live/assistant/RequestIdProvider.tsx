import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';
import { Button, Result, Spin } from 'antd';
import React, { createContext, PropsWithChildren, useContext } from 'react';

export type LiveRequestIdContextType = {
    reload: () => void;
    requestId?: string;
};

const createDefault = () => ({
    requestId: '',
    reload: () => {},
});

const LiveRequestIdContext = createContext<LiveRequestIdContextType>(createDefault());

export const useLiveRequestIdContext = () => useContext(LiveRequestIdContext);

const LiveRequestIdProvider = ({ children }: PropsWithChildren) => {
    const fetchData = async () => {
        const res = await apiCaller.get('/livescript/id/generate', {});
        if (res.code !== 0) {
            return;
        }

        return res.data;
    };

    const { data, run, loading } = useRequest(fetchData);

    if (!loading && !data) {
        return (
            <Result
                status="500"
                title="服务器出错了"
                subTitle="对不起，服务器出错了，请刷新页面重试"
                extra={
                    <Button type="primary" onClick={() => location.reload()}>
                        刷新
                    </Button>
                }
            />
        );
    }

    if (!data) {
        return <Spin spinning size="large"></Spin>;
    }

    return (
        <LiveRequestIdContext.Provider value={{ requestId: data, reload: run }}>
            <React.Fragment key={data}>{children}</React.Fragment>
        </LiveRequestIdContext.Provider>
    );
};

export default LiveRequestIdProvider;
