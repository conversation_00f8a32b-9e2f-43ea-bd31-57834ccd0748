import { apiCaller } from '@mfe/cc-api-caller-pc';
import { getUrlState } from '@src/hooks/useUrlState';
import { useRequest } from 'ahooks';
import { Flex, Popover, Space, Spin, Tag, Typography } from 'antd';
import { formatProductItem } from '../list/utils';
import { productDispatchMap, productTagMap, productTypeMap } from '@src/constants/product';
import { QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './style.scss';
import ProductDetailOverview from '@src/components/product/ProductDetail/ProductDetailOverview';
import ProductDetailTable from '@src/components/product/ProductDetail/ProductDetailTable';

const { Title, Text } = Typography;

const App = () => {
    const fetchDetail = async () => {
        const query = getUrlState();

        const res = await apiCaller.send('/product/task/searchTaskDetail', {
            taskId: query.taskId,
        });

        if (res.code !== 0) {
            return;
        }

        return formatProductItem(res.data);
    };

    const { data, loading } = useRequest(fetchDetail);

    if (!data) {
        return <Spin spinning />;
    }

    const {
        productName,
        productId,
        productNameTag,
        productTag,
        productType,
        excitation,
        validRange: { startTime, endTime },
    } = data.product;

    return (
        <Spin spinning={loading}>
            <div className="product-detail">
                <Space>
                    <Title level={4}>{productName}</Title>
                    <Title level={4}>ID {productId}</Title>
                </Space>
                <Flex justify="space-between" style={{ marginBottom: 12 }}>
                    <Space>
                        <Text strong>{productTag ? productTagMap.get(productTag) : '-'}</Text>

                        {productNameTag ? (
                            <Tag style={{ marginLeft: 4 }} bordered={false} className="product-detail__productNameTag">
                                {productTagMap.get(productNameTag)}
                                {excitation ? (
                                    <Popover
                                        color="#222"
                                        content={
                                            <div style={{ maxWidth: 300, color: '#fff' }}>
                                                活动说明：
                                                <br />
                                                {excitation}
                                            </div>
                                        }
                                    >
                                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                                    </Popover>
                                ) : null}
                            </Tag>
                        ) : null}
                    </Space>
                    <Space size={'large'}>
                        <Text type="secondary">创建时间：{dayjs(data.createTime).format('YYYY-MM-DD HH:mm')}</Text>
                        <Text type="secondary">
                            活动时间：{dayjs(startTime).format('YYYY-MM-DD')}~{dayjs(endTime).format('YYYY-MM-DD')}
                        </Text>
                        <Text type="secondary">{productTypeMap.get(productType)}</Text>
                        <Text type="secondary">{productDispatchMap.get(data.dispatchStatus)}</Text>
                    </Space>
                </Flex>

                <ProductDetailOverview data={data} />

                <ProductDetailTable productId={productId} productType={productType} taskId={data.taskId} />
            </div>
        </Spin>
    );
};

export default App;
