import {
    ProductQualifiedStatus,
    ProductStatus,
    ProductType,
    ProductTag,
    ProductDispatchStatus,
    ProductGrantStatus,
} from '@src/types/product';

export const mapToOptions = (map: Map<any, string | undefined>) => {
    return [...map.entries()].map(([value, label = '']) => ({
        value,
        label,
    }));
};

export const productTypeMap = new Map([
    [ProductType.BD, 'BD下发'],
    [ProductType.CM, 'CM下发'],
]);
export const productTypeOptions = mapToOptions(productTypeMap);

export const productTagMap = new Map<string, string>([
    [ProductTag.直接发券, '直接发券'],
    [ProductTag.充值满赠, '充值满赠'],
    [ProductTag.消耗满赠, '消耗满赠'],
    [ProductTag.有奖资源, '有奖资源'],
]);

export const productTagOptions = mapToOptions(productTagMap).filter(t => t.value !== ProductTag.有奖资源);

export const productStatusMap = new Map([
    [ProductStatus.待圈选, '待圈选'],
    [ProductStatus.已圈选, '已圈选'],
    [ProductStatus.不需要圈选, '不需要圈选'],
    [ProductStatus.待下发, '待下发'],
    [ProductStatus.已下发, '已下发'],
    [ProductStatus.已过期, '已过期'],
]);

export const productSelectionMap = new Map([
    [ProductStatus.待圈选, productStatusMap.get(ProductStatus.待圈选)],
    [ProductStatus.已圈选, productStatusMap.get(ProductStatus.已圈选)],
    [ProductStatus.不需要圈选, productStatusMap.get(ProductStatus.不需要圈选)],
]);
export const productSelectionOptions = mapToOptions(productSelectionMap);

export const productDispatchMap = new Map([
    [ProductStatus.待下发, productStatusMap.get(ProductStatus.待下发)],
    [ProductStatus.已下发, productStatusMap.get(ProductStatus.已下发)],
    [ProductStatus.已过期, productStatusMap.get(ProductStatus.已过期)],
]);

export const productDispatchOptions = [
    {
        label: productStatusMap.get(ProductStatus.待下发),
        value: ProductDispatchStatus.待下发,
    },
    {
        label: productStatusMap.get(ProductStatus.已下发),
        value: ProductDispatchStatus.已下发,
    },
    {
        label: productStatusMap.get(ProductStatus.已过期),
        value: ProductDispatchStatus.已过期,
    },
];

export const productQualifiedStatusMap = new Map([
    [ProductQualifiedStatus.MEET, '已达标'],
    [ProductQualifiedStatus.NOT_MEET, '未达标'],
]);
export const productQualifiedOptions = mapToOptions(productQualifiedStatusMap);

export const productGrantStatusMap = new Map([
    [ProductGrantStatus.待领取, '待领取'],
    [ProductGrantStatus.已领取, '已领取'],
    [ProductGrantStatus.已使用, '已使用'],
    [ProductGrantStatus.已过期, '已过期'],
]);
