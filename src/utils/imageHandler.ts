// @ts-nocheck
// createUpload缺少file参数，会报ts错误，但可正常使用
import createUpload from '@ai/mss-upload-js';

const imageHandler = (env, setSpinning, quillRef) => {
    const uploadInstance = createUpload(
        {
            // signatureUrl: `${
            //     import.meta.env.VITE_API_PREFIX
            // }/assistant/common/s3`, // 加签接口API
            // bucket: 'bdaiassistant-public',
            signatureUrl: `${import.meta.env.VITE_API_PREFIX}/livescript/sku/upload/signature`, // 加签接口API
            bucket: 'yc-as-bucket',
            s3_host: env === 'test' || env === 'dev' ? 'msstest.vip.sankuai.com' : 's3plus.sankuai.com',
            prefix_type: 's3_style',
            accept: ['.png', '.jpg'],
            hashMode: true,
            onSuccess(fileUrl) {
                setSpinning(false);
                if (!quillRef.current) {
                    return;
                }
                const quill = quillRef.current; //获取到编辑器本身
                const cursorPosition = quill.getSelection()?.index || 999999; //获取当前光标位置
                const link = fileUrl; // 图片链接
                quill.insertEmbed(cursorPosition, 'image', link); //插入图片
                quill.setSelection(cursorPosition + 1, 0); //光标位置加1
            },
            onError(errorMsg) {
                setSpinning(false);
            },
            onProgress(percent) {
                // onProgress({ percent }, file);
            },
            onStart() {},
            onFinish() {},
            validateFile: () => true,
        },
        1,
    );
    setSpinning(true);
    uploadInstance.upload();
};
export default imageHandler;
